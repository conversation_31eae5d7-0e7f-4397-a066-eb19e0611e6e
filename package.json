{"name": "php-requirements-from-composer-action", "description": "GitHub Action to extract PHP version and extension requirements from composer.json and composer.lock files", "main": "dist/index.js", "type": "module", "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "lint": "eslint src/**/*.js tests/**/*.js", "lint:fix": "eslint src/**/*.js tests/**/*.js --fix", "pack": "esbuild src/index.js --bundle --platform=node --minify --outfile=dist/index.js --external:*.exe --external:*.proto --external:*.dll", "all": "npm install && npm run lint:fix && npm run pack", "fast-all": "npm install && npm run pack", "update-versions": "node bin/update-versions.js", "get-php-version": "node bin/get-php-version.js"}, "repository": {"type": "git", "url": "git+https://github.com/impresscms-dev/php-requirements-from-composer-action.git"}, "keywords": ["github", "actions", "php", "composer", "requirements", "extensions"], "author": "<PERSON><PERSON><PERSON> (aka MekDrop)", "license": "MIT", "dependencies": {"@actions/core": "^1.10.1", "@actions/github": "^6.0.0", "dockerode": "^4.0.6", "node-fetch": "^2.7.0", "semver": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.24.0", "dump-die": "*", "ejs": "^3.1.10", "esbuild": "^0.25.3", "eslint": "^9.0.0", "eslint-plugin-github": "^6.0.0", "eslint-plugin-jest": "^28.3.0", "jest": "^29.7.0"}}