{"name": "generate-php-project-classes-list-file-action", "version": "1.0.0", "description": "GitHub action to generate a file with PHP project classes list (works only with composer projects)", "main": "dist/index.js", "type": "module", "scripts": {"pack": "ncc build src/index.js -o dist --source-map --license licenses.txt", "lint": "eslint src/**/*.js tests/**/*.js", "lint:fix": "eslint src/**/*.js tests/**/*.js --fix", "test": "jest", "all": "npm run lint && npm run pack && npm test"}, "repository": {"type": "git", "url": "git+https://github.com/impresscms-dev/generate-php-project-classes-list-file-action.git"}, "keywords": ["github", "actions", "php", "composer", "classes"], "author": "ImpressCMS", "license": "MIT", "bugs": {"url": "https://github.com/impresscms-dev/generate-php-project-classes-list-file-action/issues"}, "homepage": "https://github.com/impresscms-dev/generate-php-project-classes-list-file-action#readme", "dependencies": {"@actions/core": "^1.10.1", "@actions/exec": "^1.1.1", "ejs": "^3.1.10"}, "devDependencies": {"@vercel/ncc": "^0.38.1", "eslint": "^8.56.0", "eslint-plugin-jest": "^27.6.3", "jest": "^29.7.0", "tmp-promise": "^3.0.3"}}