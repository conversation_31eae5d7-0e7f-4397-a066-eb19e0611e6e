/**
 * Service class for interacting with GitHub API
 *
 * This class provides methods to interact with GitHub API using Octokit REST client.
 * It allows finding the latest release of a repository and downloading release assets
 * to Docker containers.
 *
 * @module services/GitHubService
 */

import {Octokit} from '@octokit/rest'

/**
 * Service class for GitHub API interactions
 */
export default class GitHubService {
    /**
     * @type {LoggerService}
     */
    #logger

    /**
     * @type {import('fs')}
     */
    #fs

    /**
     * @type {import('path')}
     */
    #path

    /**
     * @type {Octokit}
     */
    #octokit

    /**
     * Create a new GitHubService instance
     *
     * @param {LoggerService} logger - Logger service
     * @param {import('fs')} fs - File system module
     * @param {import('path')} path - Path module
     * @param {string} githubToken - GitHub API token
     */
    constructor(logger, fs, path, githubToken) {
        this.#logger = logger
        this.#fs = fs
        this.#path = path

        // Always use the Octokit REST client directly
        // If a token is provided, use it for authentication
        this.#logger.debug(githubToken ? 'Using authenticated GitHub client' : 'Using unauthenticated GitHub client')
        this.#octokit = new Octokit(githubToken ? {auth: githubToken} : {})
    }

    /**
     * Find the latest release of a repository
     *
     * @param {string} owner - Repository owner
     * @param {string} repo - Repository name
     * @returns {Promise<Object>} Latest release information
     * @throws {Error} If the repository or release cannot be found
     */
    async findLatestRepositoryRelease(owner, repo) {
        this.#logger.info(`Finding latest release for ${owner}/${repo}...`)

        const {data: latestRelease} = await this.#octokit.repos.getLatestRelease({
            owner,
            repo
        })

        this.#logger.info(`Found latest release: ${latestRelease.tag_name}`)
        return latestRelease
    }

    /**
     * Get release asset information for a specific repository, release tag, and asset name
     *
     * @param {string} owner - Repository owner
     * @param {string} repo - Repository name
     * @param {string} releaseTag - Release tag (e.g., 'v1.0.0')
     * @param {string} assetName - Name of the asset to find
     * @returns {Promise<Object>} Asset information including download URL
     * @throws {Error} If the release or asset cannot be found
     */
    async getReleaseAssetInfo(owner, repo, releaseTag, assetName) {
        // Get the release by tag
        const {data: release} = await this.#octokit.repos.getReleaseByTag({
            owner,
            repo,
            tag: releaseTag
        })

        // Find the asset by name
        const asset = release.assets.find((asset) => (asset.name === assetName))
        if (!asset) {
            throw new Error(`Asset ${assetName} not found in release ${releaseTag}`)
        }

        return asset
    }

    /**
     * Download a file from a release to a Docker container
     *
     * @param {string} owner - Repository owner
     * @param {string} repo - Repository name
     * @param {string} releaseTag - Release tag (e.g., 'v1.0.0')
     * @param {string} assetName - Name of the asset to download
     * @param {import('../Decorators/DockerContainerDecorator.js').default} container - Docker container decorator instance
     * @param {string} [containerPath='/app'] - Path in the container where the file should be placed
     * @param {string} [chmod] - Optional chmod permissions to apply (e.g., '+x')
     * @returns {Promise<void>}
     * @throws {Error} If the asset cannot be found or downloaded, or if chmod fails
     */
    async downloadReleaseAssetToContainer(owner, repo, releaseTag, assetName, container, containerPath = '/app', chmod = null) {
        this.#logger.info(`Downloading ${assetName} from ${owner}/${repo} release ${releaseTag} to container...`)

        // Get asset information
        const asset = await this.getReleaseAssetInfo(owner, repo, releaseTag, assetName)

        // Use the container's downloadFileToContainer method
        const filePath = `${containerPath}/${assetName}`
        await container.downloadFileToContainer(
            asset.browser_download_url,
            filePath,
            {
                chmod,
                headers: {
                    'User-Agent': 'impresscms-dev/php-requirements-from-composer-action'
                }
            }
        )

        this.#logger.info(`Successfully downloaded ${assetName} from ${owner}/${repo} to container`)
    }

    /**
     * Download the latest release file from a repository to a Docker container
     *
     * This method finds the latest release of a repository and downloads a specified
     * file from that release to a Docker container.
     *
     * @param {string} owner - Repository owner
     * @param {string} repo - Repository name
     * @param {string} assetName - Name of the asset to download
     * @param {import('../Decorators/DockerContainerDecorator.js').default} container - Docker container decorator instance
     * @param {string} [containerPath='/app'] - Path in the container where the file should be placed
     * @param {string} [chmod] - Optional chmod permissions to apply (e.g., '+x')
     * @returns {Promise<string>} The tag name of the latest release
     * @throws {Error} If the repository, release, or asset cannot be found or downloaded
     */
    async downloadLatestReleaseFileToContainer(owner, repo, assetName, container, containerPath = '/app', chmod = null) {
        this.#logger.info(`Finding and downloading latest ${assetName} from ${owner}/${repo} to container...`)

        // Find the latest release
        const latestRelease = await this.findLatestRepositoryRelease(owner, repo)
        this.#logger.info(`Found latest release: ${latestRelease.tag_name}`)

        // Download the asset to the container
        await this.downloadReleaseAssetToContainer(
            owner,
            repo,
            latestRelease.tag_name,
            assetName,
            container,
            containerPath,
            chmod
        )

        return latestRelease.tag_name
    }
}
