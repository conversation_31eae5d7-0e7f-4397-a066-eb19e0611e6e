/**
 * Service class for importing modules dynamically and reading files
 *
 * This class provides utility methods for dynamically importing modules,
 * extracting specific exports from them, and reading/parsing files.
 *
 * @module services/ImportService
 */

/**
 * Service class for importing modules dynamically and reading files
 */
export default class ImportService {
  /**
   * @type {Object}
   * @private
   */
  #fs;

  /**
   * @type {Object}
   * @private
   */
  #path;

  /**
   * @param {import('fs')} fs - Node.js fs module
   * @param {import('path')} path - Node.js path module
   */
  constructor(fs, path) {
    this.#fs = fs
    this.#path = path
  }
  /**
   * Import a module dynamically and extract a specific export
   *
   * @param {string} filePath - The path to the file to import
   * @param {string} [exportName] - The name of the export to extract (optional)
   * @returns {Promise<any>} The imported module or the specific export
   * @throws {Error} If the file doesn't exist or can't be imported
   */
  async importModule(filePath, exportName = null) {
    if (!this.#fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`)
    }

    const fileUrl = `file://${this.#path.resolve(filePath)}`

    const importedModule = await import(fileUrl)

    if (exportName && exportName in importedModule) {
      return importedModule[exportName]
    }

    return importedModule
  }

  /**
   * Read and parse a JSON file
   *
   * @param {string} filePath - The path to the JSON file
   * @param {Object|undefined|false} defaultValue - The default value to return if the file doesn't exist or can't be parsed
   * @returns {Object|undefined|false} The parsed JSON object, or the default value if the file doesn't exist or can't be parsed
   */
  readJsonFile(filePath, defaultValue = undefined) {
    if (!this.#fs.existsSync(filePath)) {
      return defaultValue
    }

    try {
      return JSON.parse(this.#fs.readFileSync(filePath, 'utf8'))
    } catch {
      return defaultValue
    }
  }
}
