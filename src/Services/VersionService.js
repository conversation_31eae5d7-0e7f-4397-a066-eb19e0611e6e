/**
 * Service class for version comparison and sorting operations
 *
 * This class provides utility methods for comparing and sorting version strings,
 * with support for semver-like version formats and various sorting options.
 *
 * @module services/VersionService
 */

import semver from 'semver'

/**
 * Service class for version comparison and sorting operations
 */
export default class VersionService {
  /**
   * Compare two version strings using semver rules
   *
   * @param {string} versionA - First version to compare
   * @param {string} versionB - Second version to compare
   * @returns {number} -1 if versionA < versionB, 0 if equal, 1 if versionA > versionB
   */
  compareVersions(versionA, versionB) {
    const aCoerced = semver.coerce(versionA)
    const bCoerced = semver.coerce(versionB)

    if (aCoerced && bCoerced) {
      return semver.compare(aCoerced, bCoerced)
    }

    return 0
  }

  /**
   * Check if a version satisfies a version constraint
   *
   * @param {string} version - Version to check
   * @param {string} constraint - Version constraint (e.g., ">=7.2.0")
   * @returns {boolean} True if the version satisfies the constraint
   */
  satisfiesConstraint(version, constraint) {
    try {
      const coercedVersion = semver.coerce(version)
      if (!coercedVersion) {
        return false // Skip versions that can't be coerced to semver
      }

      return semver.satisfies(coercedVersion, constraint)
    /* eslint-disable-next-line no-unused-vars */
    } catch (_) {

      return false
    }
  }

  /**
   * Sort an array of version strings
   *
   * @param {string[]} versions - Array of version strings to sort
   * @param {Object} [options] - Sorting options
   * @param {boolean} [options.descending=false] - Whether to sort in descending order
   * @returns {string[]} Sorted array of version strings
   */
  sortVersions(versions, options = {}) {
    const { descending = false } = options

    const validVersions = versions.filter((v) => semver.coerce(v))

    return [...validVersions].sort((a, b) => {
      const aCoerced = semver.coerce(a)
      const bCoerced = semver.coerce(b)
      const result = semver.compare(aCoerced, bCoerced)
      return descending ? -result : result
    })
  }

  /**
   * Filter out development and pre-release versions
   *
   * @param {string[]} versions - Array of version strings to filter
   * @param {Object} [options] - Filtering options
   * @param {boolean} [options.includeDevVersions=false] - Whether to include development versions
   * @param {boolean} [options.includePreReleases=false] - Whether to include pre-release versions (RC, beta, alpha)
   * @returns {string[]} Filtered array of version strings
   */
  filterVersions(versions, options = {}) {
    const { includeDevVersions = false, includePreReleases = false } = options

    return versions.filter((version) => {
      const isDevVersion = version.startsWith('dev-')
      if (isDevVersion && !includeDevVersions) {
        return false
      }

      const isPreRelease = version.includes('-')
      if (isPreRelease && !includePreReleases) {
        return false
      }

      return true
    })
  }

  /**
   * Sort versions by release time
   *
   * @param {Object} versionsWithTime - Object with version strings as keys and objects with time property as values
   * @param {Object} [options] - Sorting options
   * @param {boolean} [options.descending=true] - Whether to sort in descending order (newest first)
   * @returns {string[]} Array of version strings sorted by time
   */
  sortVersionsByTime(versionsWithTime, options = {}) {
    const { descending = true } = options

    const validVersions = Object.keys(versionsWithTime).filter((v) => semver.coerce(v))

    return validVersions.sort((a, b) => {
      const timeA = new Date(versionsWithTime[a].time || 0)
      const timeB = new Date(versionsWithTime[b].time || 0)
      return descending ? timeB - timeA : timeA - timeB
    })
  }

  /**
   * Extract major.minor version from a full version string
   *
   * @param {string} version - Full version string (e.g., "8.3.0")
   * @returns {string} Major.minor version (e.g., "8.3")
   */
  getMajorMinorVersion(version) {
    const coerced = semver.coerce(version)
    if (!coerced) {
      return null // Return null for versions that can't be coerced to semver
    }

    return `${coerced.major}.${coerced.minor}`
  }

  /**
   * Gets a sorted list of versions from a version map
   *
   * @param {Object} versionMap - Map of versions to their values (e.g., status)
   * @returns {Array<string>} Sorted list of versions (newest first)
   */
  getSortedVersionsFromMap(versionMap) {
    return Object.keys(versionMap).sort((a, b) => {
      const aVersion = semver.valid(semver.coerce(a)) || a
      const bVersion = semver.valid(semver.coerce(b)) || b

      return semver.compare(bVersion, aVersion) // Descending order
    })
  }

  /**
   * Extract major.minor versions from PHP version objects
   *
   * @param {Array<Object>} phpVersions - Array of PHP version objects with version property
   * @param {Array<string>} [additionalVersions=[]] - Additional versions to include
   * @returns {Array<string>} Sorted array of unique major.minor PHP versions
   */
  extractPhpMajorMinorVersions(phpVersions, additionalVersions = []) {
    const allPhpVersions = new Set()

    for (const versionInfo of phpVersions) {
      if (versionInfo && versionInfo.version) {
        const versionParts = versionInfo.version.split('.')
        if (versionParts.length >= 2) {
          allPhpVersions.add(`${versionParts[0]}.${versionParts[1]}`)
        }
      }
    }

    if (Array.isArray(additionalVersions)) {
      for (const version of additionalVersions) {
        allPhpVersions.add(version)
      }
    }

    return this.sortVersions(Array.from(allPhpVersions))
  }

  /**
   * Determine the maximum compatible Composer version for each PHP version
   *
   * @param {Array<string>} phpVersions - Array of PHP versions
   * @param {Array<Object>} composerRules - Array of Composer compatibility rules
   * @returns {Object} Map of PHP versions to their maximum compatible Composer version
   */
  determineComposerCompatibility(phpVersions, composerRules) {
    const composerVersionsMap = {}

    for (const phpVersion of phpVersions) {
      let maxCompatibleVersion = composerRules[composerRules.length - 1].composerVersion

      for (const compat of composerRules) {
        if (this.satisfiesConstraint(phpVersion, `>=${compat.minPhpVersion}`)) {
          maxCompatibleVersion = compat.composerVersion
          break // Break at first match (rules are ordered from newest to oldest)
        }
      }

      composerVersionsMap[phpVersion] = maxCompatibleVersion
    }

    return composerVersionsMap
  }
}
