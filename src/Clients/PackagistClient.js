/**
 * Helper class for fetching package information from Packagist
 *
 * This class provides methods to fetch package information from the Packagist API
 * and extract the "require" dependencies from the package metadata.
 *
 * @module clients/PackagistClient
 */

import PackagistFetchError from '../Errors/PackagistFetchError.js'
import ComposerProjectRequiredError from '../Errors/ComposerProjectRequiredError.js'
import VersionHelper from '../Helpers/VersionHelper.js'

/**
 * Helper class for fetching and processing package information from Packagist
 */
export default class PackagistClient {
  #packagistUrl

  /**
   * Create a new PackagistClient instance
   *
   * @param {string} composerProject - The composer project name (e.g., "monolog/monolog")
   * @throws {ComposerProjectRequiredError} If composerProject is not provided
   */
  constructor(composerProject, versionToolkit) {
    if (!composerProject) {
      throw new ComposerProjectRequiredError()
    }

    // Store the URL for the Packagist API

    // Format the URL for the Packagist API
    this.#packagistUrl = `https://repo.packagist.org/p2/${composerProject}.json`
    this.#versionToolkit = versionToolkit
  }

  /**
   * Load package data from Packagist
   *
   * @returns {Promise<Object>} The raw package data from Packagist
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async loadPackageData() {
    try {
      const response = await fetch(this.#packagistUrl)

      if (!response.ok) {
        throw new PackagistFetchError(
          `Failed to fetch package info: HTTP ${response.status}`,
          response.status,
          await response.text().catch(() => null)
        )
      }

      let packageData
      try {
        packageData = await response.json()
      /* eslint-disable-next-line no-unused-vars */
      } catch (_) {
        throw new PackagistFetchError(
          'Failed to parse package info: Invalid JSON response',
          response.status,
          await response.text().catch(() => null)
        )
      }

      return packageData
    } catch (error) {
      // If it's already our custom error, just rethrow it
      if (error instanceof PackagistFetchError) {
        throw error
      }

      // Otherwise, wrap it in our custom error
      throw new PackagistFetchError(
        `Failed to fetch package info: ${error.message}`,
        null,
        null
      )
    }
  }

  /**
   * Get the "require" dependencies for the package
   *
   * @param {string} [version] - Specific version to get requirements for (defaults to latest)
   * @returns {Promise<Object>} Object containing the require dependencies
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */

  /**
   * Get all available packages from the loaded data
   *
   * @returns {Promise<Object>} Object with package names as keys and their data as values
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async getAllPackages() {
    const packageData = await this.loadPackageData()

    if (!packageData.packages) {
      throw new PackagistFetchError(
        'Invalid package data: "packages" property not found',
        null,
        JSON.stringify(packageData)
      )
    }

    return packageData.packages
  }

  /**
   * Get all available versions for the package
   *
   * @param {Object} [options] - Options for filtering versions
   * @param {boolean} [options.includeDevVersions=false] - Whether to include development versions
   * @param {boolean} [options.includePreReleases=false] - Whether to include pre-release versions (RC, beta, alpha)
   * @returns {Promise<Object>} Object with version numbers as keys and their data as values
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async getAllVersions(options = {}) {
    const { includeDevVersions = false, includePreReleases = false } = options

    const packages = await this.getAllPackages()

    // Get the package name (first key in the packages object)
    const packageName = Object.keys(packages)[0]

    if (!packageName || !packages[packageName]) {
      throw new PackagistFetchError(
        'Package not found in Packagist response',
        null,
        JSON.stringify(packages)
      )
    }

    let allVersions = {}
    for(const versionData of packages[packageName]) {
      allVersions[versionData.version] = versionData
    }

    // If we want all versions, return as is
    if (includeDevVersions && includePreReleases) {
      return allVersions
    }

    // Filter versions based on options using VersionHelper
    const filteredVersions = {}
    const allVersionKeys = Object.keys(allVersions)

    // Use VersionHelper to filter versions
    const filteredVersionKeys = VersionHelper.filterVersions(allVersionKeys, {
      includeDevVersions,
      includePreReleases
    })

    // Create a new object with only the filtered versions
    for (const version of filteredVersionKeys) {
      filteredVersions[version] = allVersions[version]
    }

    return filteredVersions
  }

  /**
   * Get sorted version numbers for the package
   *
   * @param {Object} [options] - Options for filtering and sorting versions
   * @param {boolean} [options.includeDevVersions=false] - Whether to include development versions
   * @param {boolean} [options.includePreReleases=false] - Whether to include pre-release versions (RC, beta, alpha)
   * @param {boolean} [options.sortByTime=true] - Whether to sort by release time (true) or version number (false)
   * @param {boolean} [options.descending=true] - Whether to sort in descending order (newest first)
   * @returns {Promise<Array<string>>} Array of version numbers sorted according to options
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async getSortedVersions(options = {}) {
    const {
      includeDevVersions = false,
      includePreReleases = false,
      sortByTime = true,
      descending = true
    } = options

    const versions = await this.getAllVersions({ includeDevVersions, includePreReleases })

    if (Object.keys(versions).length === 0) {
      throw new PackagistFetchError(
        'No versions found for this package with the specified filters',
        null,
        JSON.stringify(versions)
      )
    }

    let sortedVersions

    if (sortByTime) {
      // Sort by release time using VersionHelper
      sortedVersions = VersionHelper.sortVersionsByTime(versions, { descending })
    } else {
      // Sort by version number using VersionHelper
      const versionKeys = Object.keys(versions)
      sortedVersions = VersionHelper.sortVersions(versionKeys, { descending })
    }

    return sortedVersions
  }

  /**
   * Get the latest version number for the package
   *
   * @param {Object} [options] - Options for filtering versions
   * @param {boolean} [options.includeDevVersions=false] - Whether to include development versions
   * @param {boolean} [options.includePreReleases=false] - Whether to include pre-release versions (RC, beta, alpha)
   * @returns {Promise<string>} The latest version number
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async getLatestVersion(options = {}) {
    const sortedVersions = await this.getSortedVersions({
      ...options,
      sortByTime: true,
      descending: true
    })

    return sortedVersions[0]
  }

  /**
   * Get the "require" dependencies for the package
   *
   * @param {string} [version] - Specific version to get requirements for (defaults to latest)
   * @returns {Promise<Object>} Object containing the require dependencies
   * @throws {PackagistFetchError} If the API request fails or returns invalid data
   */
  async getRequirements(version = null) {
    const packages = await this.getAllPackages()

    // Get the package name (first key in the packages object)
    const packageName = Object.keys(packages)[0]

    if (!packageName || !packages[packageName]) {
      throw new PackagistFetchError(
        'Package not found in Packagist response',
        null,
        JSON.stringify(packages)
      )
    }

    const packageVersions = packages[packageName]

    // If version is specified, get that specific version
    if (version && packageVersions[version]) {
      return {
        require: packageVersions[version].require || {},
        'require-dev': packageVersions[version]['require-dev'] || {}
      }
    }

    // Otherwise, get the latest version
    const latestVersion = await this.getLatestVersion()

    return {
      version: latestVersion,
      require: packageVersions[latestVersion].require || {},
      'require-dev': packageVersions[latestVersion]['require-dev'] || {}
    }
  }
}
