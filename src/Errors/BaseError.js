/**
 * Base error class for the application
 * 
 * @module errors/base-error
 */

/**
 * Base error class that all other error classes extend
 */
export default class BaseError extends Error {
  /**
   * Create a new BaseError
   * 
   * @param {string} message - Error message
   */
  constructor(message) {
    super(message)
    this.name = this.constructor.name
    Error.captureStackTrace(this, this.constructor)
  }

  /**
   * Get the error message as an array of lines for outputting
   *
   * @returns {string[]}
   */
  getOutputLines() {
    return [this.message]
  }
}
