/**
 * Error thrown when no supported PHP versions are found
 * 
 * @module errors/no-supported-php-versions-error
 */

import BaseError from './BaseError.js'

/**
 * Error thrown when no supported PHP versions are found
 */
export default class NoSupportedPhpVersionsError extends BaseError {
  /**
   * Create a new NoSupportedPhpVersionsError
   * 
   * @param {string} message - Error message
   * @param {Array} [versions] - List of PHP versions that were checked
   */
  constructor(message, versions = []) {
    super(message)
    this.versions = versions
  }
}
