<?php

namespace App\Events\Messenger;

use App\Broadcasting\Channels\UserChannel;
use App\Http\Resources\Api\Messenger\RoomResource;
use App\Models\Messenger\Room;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\SerializesModels;

/**
 * Triggered when there was something important happened on closed room for the user
 */
class NewClosedRoomMessageEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    private Room $room;
    private int $userId;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Room $room, int $userId)
    {
        $this->room = $room;
        $this->userId = $userId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return string
     */
    public function broadcastOn()
    {
        return UserChannel::name($this->userId);
    }

    public function broadcastWith(): array
    {
        $request = new Request([
            'messages-count' => 0
        ]);
        $request->setUserResolver(
            fn() => User::find($this->userId)
        );

        $response = RoomResource::make($this->room)
            ->setMessagesCount(RoomResource::PAGE_MESSAGES_COUNT)
            ->toResponse($request)
            ->getData(true);

        return $response['data'];
    }
}
