<?php

namespace App\Events\Lobbies;

use App\Contracts\HasRelatedModelInterface;
use App\Models\Lobbies\Lobby;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Queue\SerializesModels;

class LobbySavedEvent implements HasRelatedModelInterface
{
    use \Illuminate\Foundation\Bus\Dispatchable, SerializesModels;

    public function __construct(
        public readonly Lobby $lobby
    ) {

    }

    public function getModel(): Model
    {
        return $this->lobby;
    }
}
