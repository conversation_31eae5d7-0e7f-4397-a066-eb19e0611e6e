<?php

declare(strict_types=1);

namespace App\Services\Messenger;

use App\Abstracts\SystemMessageAbstract;
use App\Enums\MessageType;
use App\Enums\SharedAttachmentBasedItemType;
use App\Models\Messenger\Room;
use App\Models\Messenger\RoomMessage;
use App\Services\Content\AttachmentItemService;
use App\Services\Content\InlineFormattingService;
use App\Services\FilesService;

class RoomMessageService
{
    public function __construct(
        private readonly FilesService $fileService,
        private readonly AttachmentItemService $attachmentItemService,
        private readonly InlineFormattingService $formattingService,
    ) {

    }

    public function createUserMessage(
        int $userId,
        Room $room,
        string $message,
        array $files = [],
        MessageType|SharedAttachmentBasedItemType|null $forcedType = null,
    ): RoomMessage {

        $roomMessage = RoomMessage::create([
            'user_id' => $userId,
        ]);

        $roomMessage->message = $this->formattingService->processMessage($roomMessage, $message);
        foreach ($files as $file) {
            $this->attachmentItemService->addAttachment(
                $roomMessage,
                $this->fileService->getOldFileOrUpload($file)
            );
        }
        if ($forcedType === null) {
            $roomMessage->type = $this->detectType($roomMessage)->value;
        } else {
            $roomMessage->type = $forcedType->value;
        }

        $room->messages()->save($roomMessage);

        return $roomMessage;
    }

    public function createAnonymousMessage(Room $room, SystemMessageAbstract $message): RoomMessage
    {
        $attributes = [
            'user_id' => null,
            'message' => $message,
        ];

        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return $room->messages()->create($attributes);
    }

    /**
     * Detects room message type
     *
     * @param RoomMessage $roomMessage For what room message detect type?
     *
     * @return MessageType|SharedAttachmentBasedItemType
     */
    protected function detectType(RoomMessage $roomMessage): MessageType|SharedAttachmentBasedItemType
    {
        return $this->attachmentItemService->suggestMainContentType($roomMessage, MessageType::DEFAULT);
    }
}
