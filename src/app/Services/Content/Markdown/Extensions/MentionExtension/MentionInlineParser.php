<?php

namespace App\Services\Content\Markdown\Extensions\MentionExtension;

use App\Http\Requests\Api\UserUpdateRequest;
use App\Traits\UsesFrontend;
use League\CommonMark\Extension\Mention\Mention;
use League\CommonMark\Parser\Inline\InlineParserInterface;
use League\CommonMark\Parser\Inline\InlineParserMatch;
use League\CommonMark\Parser\InlineParserContext;

class MentionInlineParser implements InlineParserInterface
{
    use UsesFrontend;
    public function getMatchDefinition(): InlineParserMatch
    {
        return InlineParserMatch::regex('(@('.UserUpdateRequest::NICKNAME_RULE.'))');
    }

    public function parse(InlineParserContext $inlineContext): bool
    {
        $cursor = $inlineContext->getCursor();

        [,$fullMentionText, $slug] = $inlineContext->getMatches();

        $mention = new Mention(
            $fullMentionText,
            '@',
            $slug
        );
        $mention->setUrl(
            $this->getFrontendService()->route('profile', ['user_id' => strtolower($slug)])
        );

        $cursor->advanceBy($inlineContext->getFullMatchLength());
        $inlineContext->getContainer()->appendChild($mention);

        return true;
    }
}
