<?php

namespace App\Models\Games;

use App\Events\Games\GamePlatformRemovedEvent;
use App\Events\Games\GamePlatformSavedEvent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

/**
 * @property Game $game
 * @property Platform $platform
 */
class GamePlatform extends Pivot
{

    protected $table = 'game_platforms';

    protected $touches = ['game'];

    protected $dispatchesEvents = [
        'saved' => GamePlatformSavedEvent::class,
        'deleted' => GamePlatformRemovedEvent::class,
    ];

    public function game(): BelongsTo
    {
        return $this->belongsTo(Game::class);
    }

    public function platform(): BelongsTo
    {
        return $this->belongsTo(Platform::class);
    }

}
