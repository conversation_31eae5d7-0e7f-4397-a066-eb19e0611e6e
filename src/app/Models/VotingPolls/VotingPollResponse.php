<?php

namespace App\Models\VotingPolls;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use Jen<PERSON><PERSON>\Mongodb\Relations\HasOne;

class VotingPollResponse extends Model
{
    protected $connection = 'mongodb';
    protected $collection = 'voting_poll_responses';

    public function voting_poll(): HasOne
    {
        return $this->hasOne(VotingPoll::class, '_id', '_voting_poll_id');
    }
}
