<?php

namespace App\Listeners;

use App\Contracts\HasRelatedModelInterface;
use App\Jobs\UpdateSEOKeywordsJob;
use Illuminate\Contracts\Queue\ShouldQueue;

class UpdateSEOItemDataListener implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param HasRelatedModelInterface $event
     *
     * @return void
     */
    public function handle(HasRelatedModelInterface $event)
    {
        $model = $event->getModel();

        UpdateSEOKeywordsJob::dispatch($model);
    }
}
