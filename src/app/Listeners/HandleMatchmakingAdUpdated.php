<?php

namespace App\Listeners;

use App\Events\MatchmakingAdUpdatedEvent;
use App\Repositories\FeedItemRepository;

class HandleMatchmakingAdUpdated
{
    public function __construct(
        protected readonly FeedItemRepository $feedItemRepository
    ) {

    }

    public function handle(MatchmakingAdUpdatedEvent $event)
    {
        $matchmakingAd = $event->getMatchmakingAd();

        $postIds = $matchmakingAd->attached_to_posts()->pluck('_id')->toArray();

        $this->feedItemRepository->updateGameIdItemsByPostIds($postIds, $matchmakingAd->_game_id);
    }
}
