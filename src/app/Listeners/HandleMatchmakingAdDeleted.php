<?php

namespace App\Listeners;

use App\Events\MatchmakingAdDeletedEvent;
use App\Models\Post;
use App\Models\UnresolvedAttachmentItem;

class HandleMatchmakingAdDeleted
{
    public function handle(MatchmakingAdDeletedEvent $event)
    {
        $matchmakingAd = $event->getMatchmakingAd();

        $unresolvedItem = UnresolvedAttachmentItem::create([
            'url' => $matchmakingAd->getInternalAttachmentUrl(),
            'reason' => 'Removed',
            'retryable' => false,
        ]);

        /**
         * @var Post $post
         */
        foreach ($matchmakingAd->attached_to_posts->toArray() as $post) {
            $post->attached_matchmakingAds()->detach($matchmakingAd);
            $post->attached_unresolved()->attach($unresolvedItem);
            $post->save();
        }
    }
}
