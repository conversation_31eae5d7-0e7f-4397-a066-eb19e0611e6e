<?php

namespace App\Jobs\Attachments;

use App\Enums\QueueName;
use App\Events\Content\LinkImageUpdatedEvent;
use App\Exceptions\BrowserNotFoundException;
use App\Models\Link;
use App\Services\Content\AttachmentItemHandlers\HttpLinkHandler;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class MakeLinkScreenshotJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Link $link;

    /**
     * How many times to max retry before failing?
     *
     * @var int
     */
    public $tries = 10;

    public int $timeout = 120;
    public bool $failOnTimeout = true;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Link $link)
    {
        $this->link = $link;
        $this->onQueue(QueueName::Links->value);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(HttpLinkHandler $linkService)
    {
        try {
            $linkService->takeScreenshot($this->link);

            LinkImageUpdatedEvent::broadcast($this->link);

            Log::error('Screenshot taken for link: ' . $this->link->id);
        } catch (BrowserNotFoundException $browserNotFoundException) {
            Log::error('Browser not found');
            // if we are here - probably chrome was not found
            // retry in another day
            $this->release(86400);
            return;
        } catch (Throwable $exception) {
            Log::error('Link making screenshot failed: ' . $exception->getMessage());
            // any other exception
            // retries in 15 minutes * $attempts
            $this->release(
                900 * $this->attempts()
            );
            return;
        }
    }
}
