<?php

namespace App\Jobs\Translations;

use App\Contracts\HasTranslationsModelInterface;
use App\Enums\QueueName;
use App\Services\Content\TranslationsService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AutoTranslateJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    public function __construct(
        protected readonly HasTranslationsModelInterface $item,
        protected readonly string                        $language,
        protected readonly array                         $fieldsToTranslate,
    )
    {
        $this->onQueue(QueueName::Translation->value);
    }

    public function handle(TranslationsService $translationsService)
    {
        if (!config('content.autotranslated') || app()->environment('testing')) {
            return;
        }

        $translatableField = $translationsService->getFirstTranslatableField($this->item);

        $originalLanguage = $this->item->getOriginalLanguage();
        if ($originalLanguage === $this->language) {
            return;
        }

        if (!$originalLanguage) {
            $this->release(6000);

            return;
        }

        if (!$translationsService->canBeTranslated($this->item->getTranslation($translatableField, $originalLanguage), true)) {
            return;
        }

        $originTranslationsAndFieldsMap = $this->createOriginalTextAndFieldsMap($originalLanguage);

        if (empty($originTranslationsAndFieldsMap)) {
            return;
        }

        $translations = $translationsService->autoTranslate(
            array_keys($originTranslationsAndFieldsMap),
            $this->language
        );

        foreach ($translations as $originalText => $translatedText) {
            foreach ($originTranslationsAndFieldsMap[$originalText] as $field) {
                $this->item->setTranslation($field, $this->language, $translatedText);
            }
        }

        $this->item->save();
    }

    private function createOriginalTextAndFieldsMap(string $originalLanguage): array
    {
        $originTranslationsAndFieldsMap = [];
        foreach ($this->fieldsToTranslate as $field) {
            $text = $this->item->getTranslation($field, $originalLanguage);

            if (!$text) {
                continue;
            }

            if (isset($originTranslationsAndFieldsMap[$text])) {
                $originTranslationsAndFieldsMap[$text][] = $field;
            } else {
                $originTranslationsAndFieldsMap[$text] = [$field];
            }
        }

        return $originTranslationsAndFieldsMap;
    }
}
