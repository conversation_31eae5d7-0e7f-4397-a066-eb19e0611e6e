<?php

namespace App\Jobs\Content;

use App\Enums\QueueName;
use App\Models\ItemInfo\HashTag;
use App\Repositories\HashTagRepository;
use App\Services\Content\HashTagsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\ThrottlesExceptions;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class UpdateHashTagExtraInfoJob implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected readonly HashTag $hashTag,
        protected readonly bool $force = false,
    )
    {
        $this->onQueue(QueueName::HashTags->value);
    }

    public function middleware()
    {
        return [
            (new ThrottlesExceptions(10, 5))->backoff(5)
        ];
    }

    public function handle(HashTagsService $hashTagsService, HashTagRepository $hashTagRepository)
    {
        if (!$this->force && app()->environment('testing')) {
            return;
        }

        try {
            $description = $hashTagsService->describeWithOpenAI($this->hashTag);
        } catch (Throwable $e) {
            $this->fail($e);
            return;
        }

        if (!isset($description['description'])) {
            $this->release(3000);

            return;
        }

        $this->hashTag->description = $description['description'];
        $this->hashTag->restriction = $description['moderation'] ?? '';

        if ($this->failOrSave()) {
            return;
        }

        if (!empty($description['writen_hashtag'])) {
            $this->hashTag->hashtag = $description['writen_hashtag'];
        }

        if ($this->failOrSave()) {
            return;
        }

        if (!empty($description['related'])) {
            try {
                $resolvedHashtags = $hashTagRepository->findOrCreateInstances($description['related']);

                foreach ($resolvedHashtags as $resolvedHashTag) {
                    $this->hashTag->relatedHashTags()->attach($resolvedHashTag->id);
                }
            } catch (Throwable $e) {
                Log::error($e->getMessage(), ['hashtag' => $this->hashTag->id]);
            }
        }

        $this->hashTag->save();
    }

    private function failOrSave(): bool
    {
        try {
            $this->hashTag->save();

            return false;
        } catch (Throwable $e) {
            $this->fail($e);
            return true;
        }
    }

}
