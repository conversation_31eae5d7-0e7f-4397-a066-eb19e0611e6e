<?php

namespace App\Jobs\Helpers;

use App\Enums\QueueName;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use ReflectionClass;

class DispatchGeneratedBatchJob implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected readonly string $jobClass,
        protected readonly array $matrix,
    ) {
        $this->onQueue(QueueName::Misc->value);
    }

    public function handle()
    {
        Bus::batch(
            $this->generateJobs()
        )
            ->onQueue($this->queue ?? QueueName::Misc->value)
            ->dispatch()
        ;
    }

    protected function getAllArgumentsVariations(): array
    {
        $result = [[]];

        foreach ($this->matrix as $variationGroup) {
            $currentResult = [];

            foreach ($result as $resultItem) {
                foreach ((array)$variationGroup as $value) {
                    $currentResult[] = array_merge($resultItem, [$value]);
                }
            }

            $result = $currentResult;
        }

        return $result;
    }

    protected function generateJobs(): array {
        $ret = [];

        $reflectionClass = new ReflectionClass($this->jobClass);
        foreach ($this->getAllArgumentsVariations() as $arguments) {
            $ret[] = $reflectionClass->newInstanceArgs($arguments);
        }

        return $ret;
    }

}
