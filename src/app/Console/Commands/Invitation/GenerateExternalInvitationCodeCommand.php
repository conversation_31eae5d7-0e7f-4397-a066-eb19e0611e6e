<?php

namespace App\Console\Commands\Invitation;

use App\Services\ExternalInvitationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use RuntimeException;
use Throwable;

class GenerateExternalInvitationCodeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'invitation:code {count=1 : How many code to generate?} {expires_at? : When code will expire?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generates external invitation code';

    private ExternalInvitationService $externalInvitationService;

    public function __construct(ExternalInvitationService $externalInvitationService)
    {
        $this->externalInvitationService = $externalInvitationService;

        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $args = $this->getValidatedArguments();
        } catch (Throwable $throwable) {
            $this->error(
                $throwable->getMessage()
            );
            return 1;
        }

        $this->line('Code(s) created:');
        for ($i = 0; $i < $args['count']; $i++) {
            $invitation = $this->externalInvitationService->generateCodeBasedInvitation($args['expires_at']);

            $this->line(
                sprintf("  %d. %s", $i + 1, $invitation->code)
            );
        }

        return 0;
    }

    protected function getValidatedArguments() {
        $validator = Validator::make([
            'count' => $this->argument('count'),
            'expires_at' => $this->argument('expires_at'),
        ], [
            'count' => [
                'integer',
                'min:1',
                'required',
            ],
            'expires_at' => [
                'date',
                'nullable',
                'after:today',
            ],
        ]);

        if ($validator->fails()) {
            throw new RuntimeException(
                $validator->errors()->first()
            );
        }

        return $validator->validated();
    }
}
