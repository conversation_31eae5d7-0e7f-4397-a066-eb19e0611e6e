<?php

namespace App\Console\Commands\SEO;

use App\Enums\FrontendPageRoute;
use App\Services\PostService;
use App\Services\ProfileAssets\GameService;
use App\Services\User\UserService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Model;
use MongoDB\Exception\UnsupportedException;

class TriggerFrontendPrerenderCommand extends Command
{

    protected $signature = 'seo:prerender:frontend { route_name : Route name } { id : Id of item to prerender }';
    protected $description = 'Triggers pre-rendering service for specific frontend item';

    public function handle(
        PostService $postService,
        UserService $userService,
        GameService $gameService,
    )
    {
        $routeName = FrontendPageRoute::from(
            $this->argument('route_name')
        );
        $model = $this->getModel($routeName, $this->argument('id'));

        switch ($routeName) {
            case FrontendPageRoute::PROFILE:
                /**
                 * @noinspection PhpParamsInspection
                 * @noinspection UnknownInspectionInspection
                 */
                $userService->dispatchPrerendering($model, true);
            break;
            case FrontendPageRoute::GAME:
                /**
                 * @noinspection PhpParamsInspection
                 * @noinspection UnknownInspectionInspection
                 */
                $gameService->dispatchPrerendering($model, true);
                break;
            case FrontendPageRoute::POST_SPECIFIC:
                /**
                 * @noinspection PhpParamsInspection
                 * @noinspection UnknownInspectionInspection
                 */
                $postService->dispatchPrerendering($model, true);
                break;
            default:
                throw new UnsupportedException();
        }

        $this->output->writeln('Pre-rendering scheduled.');
    }

    private function routeNameToModelName(FrontendPageRoute $frontendPageRoute): string
    {
        $class = $frontendPageRoute->getEnumCaseRelatedModelClass();
        if (!$class) {
            throw new UnsupportedException();
        }

        return $class;
    }

    private function getModel(FrontendPageRoute $frontendPageRoute, string $id): Model
    {
        $class = $this->routeNameToModelName($frontendPageRoute);

        /**
         * @noinspection PhpUndefinedMethodInspection
         * @noinspection UnknownInspectionInspection
         */
        return $class::findOrFail($id);
    }

}
