<?php

namespace App\Exceptions\Auth;

use App\Traits\ResponseTrait;
use Exception;
use Illuminate\Http\JsonResponse;
use Throwable;

class AnotherUserRegisteredWithSuchConnectedAccount extends Exception
{
    use ResponseTrait;

    public function __construct(int $code = 0, ?Throwable $previous = null)
    {
        parent::__construct(trans('auth.another_user_has_this_connected_account'), $code, $previous);
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(): JsonResponse
    {
        return $this->exception($this);
    }

}
