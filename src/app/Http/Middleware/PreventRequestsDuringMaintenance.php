<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance as Middleware;

class PreventRequestsDuringMaintenance extends Middleware
{
    /**
     * The URIs that should be reachable while maintenance mode is enabled.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
    ];

    protected function getHeaders($data)
    {
        $headers = parent::getHeaders($data);
        $headers['Access-Control-Allow-Origin'] = '*';
        $headers['Access-Control-Allow-Methods'] = 'POST, GET, OPTIONS, PUT, DELETE, HEAD';
        $headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With';
        $headers['Access-Control-Expose-Headers'] = 'X-Reason';
        $headers['X-Reason'] = 'maintenance';

        return $headers;
    }
}
