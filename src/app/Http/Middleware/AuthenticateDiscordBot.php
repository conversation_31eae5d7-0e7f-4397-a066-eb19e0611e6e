<?php

namespace App\Http\Middleware;

use Closure;

class AuthenticateDiscordBot
{
    use \App\Traits\ResponseTrait;

    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure  $next
     * @param  string|null  $redirectToRoute
     * @return Response|RedirectResponse|null
     */
    public function handle($request, Closure $next, $redirectToRoute = null)
    {
        if ($request->user()->is_discord_bot === 0) {
            return $this->unauthorized();
        }

        return $next($request);
    }
}
