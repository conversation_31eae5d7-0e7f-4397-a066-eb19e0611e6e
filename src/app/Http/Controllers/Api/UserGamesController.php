<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UserGamesUpdateRequest;
use App\Http\Resources\Api\Games\GameResource;
use App\Models\User;
use App\OA as OA;
use App\Services\User\UserService;
use App\Traits\ResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @\OA\Tag("User games")
 */
class UserGamesController extends Controller
{
    use ResponseTrait;

    private UserService $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    /**
     * List all user games
     *
     * @OA\ResponseClass(status=200, asList=1, GameResource::class)
     *
     * @OA\ResponseError(status=401, content="Unauthorized")
     *
     * @\OA\Secured
     */
    public function list(Request $request, ?int $userId = null): JsonResponse
    {

        /** @var User $user */
        if ($userId === null) {
            $user = $request->user();
        } else {
            $user = User::with('games')->findOrFail($userId);
        }

        return $this->success(GameResource::collection($user->games->all()));
    }

    /**
     * Set user games
     *
     * @OA\ResponseClass(status=200, asList=1, GameResource::class)
     *
     * @OA\ResponseError(status=400)
     * @OA\ResponseError(status=401, content="Unauthorized")
     *
     * @\OA\Secured
     */
    public function update(UserGamesUpdateRequest $request): JsonResponse
    {
        /** @var User $user */
        $user = $request->user();

        $validated = $request->validatedAndDehashed();

        $errors = $this->userService->updateUserGames($user, $validated);
        if ($errors !== null) {
            return $this->error($errors);
        }

        return $this->success(
            GameResource::collection(
                $user->games()->get()
            )
        );
    }
}
