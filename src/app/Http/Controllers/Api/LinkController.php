<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Content\LinkDescriptionRequest;
use App\Http\Requests\Api\Content\LinkImageRequest;
use App\Http\Requests\Api\Content\LinkItemRequest;
use App\Http\Resources\Api\AttachmentItems\LinkResource;
use App\Models\Link;
use App\OA as OA;
use App\Services\Content\AttachmentItemService;
use App\Traits\ResponseTrait;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Psr\Container\NotFoundExceptionInterface;

/**
 * @\OA\Tag("Links")
 */
class LinkController extends Controller
{
    use ResponseTrait;

    private AttachmentItemService $attachmentItemService;

    public function __construct(AttachmentItemService $linksService)
    {
        $this->attachmentItemService = $linksService;
    }

    /**
     * Get specific image (link) binary content
     *
     * @OA\Response(status=200, content="RAW_JPEG.....", contentType="image/jpeg")
     *
     * @OA\ResponseError(status=404, content="File not found")
     */
    public function getImage(LinkImageRequest $request)
    {
        $validated = $request->validated();

        /**
         * @var Link $link
         */
        $link = Link::findOrFail($validated['linkId']);

        if (!$link->has_image) {
            return $this->notFound();
        }

        try {
            $path = $this->attachmentItemService->prepareImageForResponse(
                $link,
                $request->has('width') ? (int)$request->get('width') : null,
                $request->has('height') ? (int)$request->get('height') : null
            );
        } catch (FileNotFoundException|NotFoundExceptionInterface $e) {
            return $this->notFound();
        }

        return Storage::disk('user-images')->response($path);
    }

    /**
     * Get specific image (link) properties by url
     *
     * @OA\ResponseClass(status=200, LinkResource::class)
     *
     * @OA\RequestBodyEmpty()
     *
     * @OA\ResponseError(status=404, content="File not found")
     */
    public function describe(LinkDescriptionRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $link = $this->attachmentItemService->resolve([
            $validated['url']
        ])[0];

        return $this->success(
            LinkResource::make($link)->toArray($request)
        );
    }

    /**
     * Get specific image (link) properties by linkId
     *
     * @OA\ResponseClass(status=200, LinkResource::class)
     *
     * @OA\RequestBodyEmpty()
     *
     * @OA\ResponseError(status=404, content="File not found")
     */
    public function item(LinkItemRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $link = Link::findOrFail($validated['linkId']);

        return $this->success(
            LinkResource::make($link)->toArray($request)
        );
    }

}
