<?php

declare(strict_types=1);

namespace App\Http\Requests\User\Blocks;

use App\Rules\UserFriendExistsRule;
use Illuminate\Foundation\Http\FormRequest;

class UserUnfriendRequest extends FormRequest
{
    public function all($keys = null): array
    {
        $data = parent::all($keys);
        $data['userId'] = $this->route('userId');

        return $data;
    }

    public function rules()
    {
        return [
            'userId' => [
                'required',
                'numeric',
                new UserFriendExistsRule($this->user()->id),
            ],
        ];
    }
}
