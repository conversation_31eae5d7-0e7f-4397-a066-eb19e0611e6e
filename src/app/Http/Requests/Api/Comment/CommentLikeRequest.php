<?php

namespace App\Http\Requests\Api\Comment;

use App\Abstracts\LikeRequestAbstract;

class CommentLikeRequest extends LikeRequestAbstract
{

    public function all($keys = null): array
    {
        $data = parent::all($keys);
        $data['commentId'] = $this->route('commentId');

        return $data;
    }

    public function rules()
    {
        $rules = parent::rules();
        $rules['commentId'] = [
            'required',
            'string',
        ];

        return $rules;
    }

}
