<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Http\Resources\Api\MetaDataResource;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\PropertyIgnore("resource")
 *
 * @property User $resource
 *
 * @OA\Property("id", description="Unique user id", type="string")
 * @OA\Property("name", description="Username", type="string")
 * @OA\Property("nickname", description="Nickname", type="email")
 * @OA\Property("data", type=UserDataResource::class, description="User settings")
 * @OA\Property("country", description="Country code", type="string")
 * @OA\Property("timezone", description="Timezone name", type="string")
 * @OA\Property("leveling", type=LevelingResource::class, description="User leveling information")
 * @OA\Property("wallet_address", description="Metamas wallet address", type="string")
 * @OA\Property("discord_id", description="Discord ID", type="string")
 * @OA\Property("is_verified", description="Verification flag", type="bool", example="false")
 * @OA\Property("is_muted", description="Is muted flag", type="bool", example="false")
 * @OA\Property("meta", type=MetaDataResource::class, description="Meta tags data")
 * @OA\Property("slug", description="Good looking text for URL (SEO)", type="string", example="user1")
 */
class UserResource extends JsonResource
{
    public function toArray($request = null)
    {
        return [
            'id' => $this->resource->getRouteKey(),
            'slug' => $this->resource->slug,
            'name' => $this->when(
                !($this instanceof UserFriendResource),
                $this->resource->name
            ),
            'nickname' => $this->resource->nickname,
            'data' => $this->when(
                $this->resource->data !== null,
                new UserDataResource($this->resource->data)
            ),
            'country' => $this->resource->country,
            'timezone' => $this->resource->timezone,
            'leveling' => (new LevelingResource($this->resource))->toArray(),
            'languages' => $this->resource->languages,
            'wallet_address' => $this->resource->wallet_address,
            'is_verified' => (bool) $this->resource->is_verified,
            'discord_id' => $this->resource->discord_id ?? null,
            'is_muted' => (bool) $this->resource->is_muted,
            'meta' => MetaDataResource::make($this->resource),
        ];
    }
}
