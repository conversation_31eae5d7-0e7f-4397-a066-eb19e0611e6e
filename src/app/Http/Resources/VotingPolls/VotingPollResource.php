<?php

namespace App\Http\Resources\VotingPolls;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @OA\Property("id", type="string", description="Poll ID")
 * @OA\Property("title", type="string", description="Poll title")
 * @OA\Property("timestamp_from", type="number", description="Timestamp when the poll becomes active")
 * @OA\Property("timestamp_from", type="number", description="Timestamp when the poll becomes inactive")
 * @OA\Property("questions", type="array", description="Poll questions with choices and results")
 */
class VotingPollResource extends JsonResource
{
    public function toArray($request)
    {
        $userId = $request->user()->id;
        $pollResponse = $this->resource->voting_poll_responses()
            ->where('_author_id', $userId)
            ->first();

        return [
            'id' => $this->resource->getRouteKey(),
            'title' => $this->resource->title,
            'timestamp_from' => $this->resource->timestamp_from,
            'timestamp_to' => $this->resource->timestamp_to,
            'questions' => $this->resource->questions,
            'is_voted' => ($pollResponse !== null),
            'answers' => ($pollResponse !== null) ? $pollResponse->answers : null,
        ];
    }
}
