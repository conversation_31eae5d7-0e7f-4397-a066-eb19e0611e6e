<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class MetaDataIncludedResourceCollection extends AnonymousResourceCollection
{

    private array $meta = [];

    public function setWebFeedUrl(string $url, string $title): static
    {
        $this->meta['web_feed_url'] = $url;
        $this->meta['web_feed_title'] = $title;

        return $this;
    }

    public function setMetaInfo(string $key, string|int|float|array|bool $value): static
    {
        $this->meta[$key] = $value;

        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     *
     * @return array
     */
    public function toArray($request)
    {
        return [
            'data' => parent::toArray($request),
            'meta' => $this->meta,
        ];
    }

}
