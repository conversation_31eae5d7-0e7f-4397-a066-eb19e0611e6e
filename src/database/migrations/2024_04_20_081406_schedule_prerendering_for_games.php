<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;
use Symfony\Component\Console\Output\NullOutput;

class SchedulePrerenderingForGames extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (app()->environment('testing')) {
            // skipping for tests environments

            return;
        }

        $allCount = $this->buildBaseQuery()->count();

        $consoleOutput = new ConsoleOutput();
        $nullOutput = new NullOutput();
        $progressBar = new ProgressBar(
            $consoleOutput
        );
        $progressBar->setMessage("Scheduling prerendering for games...");
        $progressBar->setMaxSteps($allCount);

        $i = 0;
        $this->buildBaseQuery()->chunk(100, function($rows) use ($nullOutput, $consoleOutput, $progressBar, &$i) {
            foreach ($rows as $row) {
                try {
                    $progressBar->display();
                    Artisan::call('seo:prerender:frontend', [
                        'route_name' => 'game',
                        'id' => $row->id,
                    ], $nullOutput);
                } catch (Throwable $e) {
                    $consoleOutput->writeln(
                        $e->getMessage()
                    );

                    // ignore error
                    break;
                }
                $progressBar->setProgress(++$i);
            }
        });

        $consoleOutput->writeln('Done.');
    }

    protected function buildBaseQuery(): Builder
    {
        /**
         * @noinspection PhpParamsInspection
         * @noinspection UnknownInspectionInspection
         */
        return DB::table('games')
            ->orderBy('id', 'asc');
    }
}
