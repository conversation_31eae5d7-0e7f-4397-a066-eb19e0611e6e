<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class GenerateLobbiesKeywords extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        while(true) {
            $items = DB::connection('mongodb')->table('lobbies')->whereNull('keywords')->limit(100)->get();
            if (!count($items)) {
                break;
            }
            foreach ($items as $item) {
                $keywords = $this->generateKeywords((object)$item);
                DB::connection('mongodb')->table('lobbies')->where('_id', $item['_id'])->update(['keywords' => $keywords]);
            }
        }
    }

    /**
     * @noinspection DuplicatedCode
     * @noinspection UnknownInspectionInspection
     */
    private function generateKeywords($item): string {
        $game = DB::table('games')->where('id', $item->_game_id)->first();
        $text = sprintf("%d/%d are waiting in '%s' lobby for '%s' game. They plan to talk in %s.",
            $item->team_size,
            count($item->user_ids),
            $item->title,
            $game?->name,
            implode(', ', $item->languages)
        );
        $text = preg_replace('/[^\p{L}\p{N}\s#]/u', ' ', $text);
        $words = str_word_count(strtolower($text), 1);

        $stopWords = [
            "a", "an", "and", "are", "as", "at", "for", "from", "how", "i", "in", "is",
            "it", "of", "on", "or", "that", "the", "this", "to", "was", "what", "when",
            "where", "who", "will", "with", "they"
        ];

        foreach ($words as $word) {
            if (!in_array($word, $stopWords, true)) {
                if (!isset($wordFrequency[$word])) {
                    $wordFrequency[$word] = 1;
                } else {
                    $wordFrequency[$word]++;
                }
            }
        }

        arsort($wordFrequency);

        $ret = array_slice(array_keys($wordFrequency), 0, 10);

        return implode(', ', $ret);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // do not reverse
    }
}
