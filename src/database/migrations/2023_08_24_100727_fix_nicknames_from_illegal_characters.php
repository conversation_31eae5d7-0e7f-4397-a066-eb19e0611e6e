<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class FixNicknamesFromIllegalCharacters extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $users = DB::table('users')->get();

        foreach ($users as $user) {
            $originalNickname = $user->nickname;
            $newNickname = preg_replace('/[^A-Za-z0-9_\-]+/u', '_', $originalNickname);

            if ($originalNickname === $newNickname) {
                continue;
            }

            // Check if the new nickname already exists
            $suffix = 1;
            while (DB::table('users')->where('nickname', $newNickname)->where('id', '<>', $user->id)->exists()) {
                $newNickname = $originalNickname . $suffix;
                $suffix++;
            }

            DB::table('users')->where('id', $user->id)->update([
                'nickname' => $newNickname,
            ]);
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // do nothing - not reversible
    }
}
