/**
 * Factory for Node.js built-in modules
 *
 * This class provides static methods to get Node.js built-in modules.
 * It allows accessing Node.js core modules through the dependency injection container
 * without needing to directly import them in each service.
 *
 * @module factories/NodeModuleFactory
 */

import fs from 'fs'
import path from 'path'
import Docker from 'dockerode'

/**
 * Factory class for Node.js built-in modules
 */
export default class NodeModuleFactory {
  /**
   * Get the fs module instance
   *
   * @returns {import('fs')} The fs module instance
   */
  static getFsInstance() {
    return fs
  }

  /**
   * Get the path module instance
   *
   * @returns {import('path')} The path module instance
   */
  static getPathInstance() {
    return path
  }

  /**
   * Get the dockerode module instance
   *
   * @returns {import('dockerode')} The dockerode module instance
   */
  static getDockerodeInstance() {
    return Docker
  }
}
