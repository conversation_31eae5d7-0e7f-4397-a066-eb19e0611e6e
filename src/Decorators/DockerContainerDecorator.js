/**
 * Decorator for dockerode container instances
 * Provides utility methods for working with Docker containers
 *
 * @module decorators/docker-container-decorator
 */

import DockerContainerError from '../Errors/DockerContainerError.js'
import ContainerNotRunningError from '../Errors/ContainerNotRunningError.js'
import stripAnsi from 'strip-ansi'
import tarFs from 'tar-fs'

/**
 * Decorator for dockerode container instances
 * Provides utility methods for working with Docker containers
 */
export default class DockerContainerDecorator {
  /**
   * @type {import('dockerode').Container}
   */
  #container

  /**
   * @type {LoggerService}
   */
  #logger

  /**
   * Create a new DockerContainerDecorator instance
   *
   * @param {import('dockerode').Container} container - Dockerode container instance
   * @param {LoggerService} logger - Logger service
   */
  constructor(container, logger) {
    if (!container) {
      throw new Error('Container is required')
    }

    if (!logger) {
      throw new Error('Logger is required')
    }

    this.#container = container
    this.#logger = logger
  }

  /**
   * Execute a command in the container and return its output
   *
   * @param {string[]} cmd - Command to execute in the container
   * @param {string} errorPrefix - Prefix for error messages
   * @param {Function} errorConstructor - Constructor for command-specific errors
   * @returns {Promise<string>} Command output
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {Error} Command-specific error if command fails
   */
  async executeContainerCommand(cmd, errorPrefix, errorConstructor = DockerContainerError) {
    if (!this.#container) {
      throw new ContainerNotRunningError()
    }

    this.#logger.debug(`Executing command in container: ${cmd.join(' ')}`)

    const exec = await this.#container.exec({
      Cmd: cmd,
      AttachStdout: true,
      AttachStderr: true,
      Env: [
        'LANG=C.UTF-8',
        'LC_ALL=C.UTF-8',
        'NO_COLOR=1'
      ]
    })

    const stream = await exec.start({
      hijack: true
    })

    let buffer = Buffer.alloc(1024)
    let bufferLength = 0
    await new Promise((resolve, reject) => {
      stream.on('data', (chunk) => {
        [buffer, bufferLength] = this.#expandBufferIfNeeded(buffer, bufferLength, chunk)
      })

      stream.on('end', resolve)
      stream.on('error', reject)
    })
    buffer = buffer.subarray(0, bufferLength)

    const output = stripAnsi(
      this.#demuxStreamData(buffer)
    )

    const { ExitCode } = await exec.inspect()
    if (ExitCode !== 0) {
      throw new errorConstructor(`${errorPrefix} with exit code ${ExitCode}`, ExitCode, output)
    }

    return output
  }

  /**
   * Check if a file exists in the container
   *
   * @param {string} filePath - Path to the file in the container
   * @returns {Promise<boolean>} True if the file exists, false otherwise
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {DockerContainerError} If there's an error executing the command
   */
  async fileExistsInContainer(filePath) {
    if (!this.#container) {
      throw new ContainerNotRunningError()
    }

    this.#logger.debug(`Checking if file exists in container: ${filePath}`)

    try {
      const exec = await this.#container.exec({
        Cmd: ['test', '-f', filePath],
        AttachStdout: false,
        AttachStderr: false
      })

      await exec.start()
      const { ExitCode } = await exec.inspect()

      // ExitCode 0 means the file exists
      return ExitCode === 0
    } catch (error) {
      throw new DockerContainerError(`Failed to check if file exists in container: ${filePath}`, this.#container?.id, error)
    }
  }

  /**
   * Upload files from a local directory to the container
   *
   * @param {string} sourcePath - Local source path
   * @param {string} destinationPath - Destination path in the container
   * @returns {Promise<void>}
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {DockerContainerError} If there's an error uploading the files
   */
  async uploadFilesToContainer(sourcePath, destinationPath) {
    if (!this.#container) {
      throw new ContainerNotRunningError()
    }

    this.#logger.info(`Uploading files from ${sourcePath} to container at ${destinationPath}...`)

    try {
      // Create the destination directory if it doesn't exist
      await this.executeContainerCommand(
        ['mkdir', '-p', destinationPath],
        'Failed to create destination directory'
      )

      // Create a tar archive from the source directory
      const tarStream = tarFs.pack(sourcePath)

      // Upload the tar archive to the container
      await this.#container.putArchive(tarStream, { path: destinationPath })

      this.#logger.info(`Successfully uploaded files to ${destinationPath}`)
    } catch (error) {
      if (error instanceof ContainerNotRunningError) {
        throw error
      }
      throw new DockerContainerError(`Failed to upload files to container: ${error.message}`, this.#container?.id, error)
    }
  }

  /**
   * Read a text file from the container
   *
   * @param {string} filePath - Path to the file in the container
   * @returns {Promise<string>} Content of the file
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {DockerContainerError} If there's an error reading the file
   */
  async readTextFile(filePath) {
    return this.executeContainerCommand(['cat', filePath], `Failed to read file: ${filePath}`)
  }

  /**
   * Remove files or directories in the container
   *
   * @param {string} path - Path to remove in the container
   * @param {Object} options - Options for removal
   * @param {boolean} options.recursive - Whether to remove recursively (default: true)
   * @param {boolean} options.force - Whether to force removal (default: true)
   * @returns {Promise<void>}
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {DockerContainerError} If there's an error removing the files
   */
  async removeFiles(path, options = {}) {
    const recursive = options.recursive !== false
    const force = options.force !== false

    const args = ['rm']
    if (recursive) args.push('-r')
    if (force) args.push('-f')
    args.push(path)

    await this.executeContainerCommand(args, `Failed to remove ${path}`)
  }

  /**
   * Apply chmod permissions to a file in the container
   *
   * @param {string} filePath - Path to the file in the container
   * @param {string} chmod - Chmod permissions to apply (e.g., '+x')
   * @returns {Promise<void>}
   * @throws {ContainerNotRunningError} If container is not running
   * @throws {DockerContainerError} If chmod fails
   */
  async applyChmodToFile(filePath, chmod) {
    this.#logger.info(`Applying chmod ${chmod} to ${filePath}...`)

    try {
      // First, try to use the standard chmod command
      await this.executeContainerCommand(
        ['chmod', chmod, filePath],
        `Failed to apply chmod ${chmod} to ${filePath}`
      )

      this.#logger.info(`Successfully applied chmod ${chmod} to ${filePath}`)
    } catch (error) {
      this.#logger.warn(`chmod command failed: ${error.message}`)

      // If standard chmod fails, try using a shell command as a fallback
      this.#logger.info(`Trying alternative chmod approach with shell command...`)

      try {
        await this.executeContainerCommand(
          ['sh', '-c', `chmod ${chmod} ${filePath}`],
          `Failed to apply chmod ${chmod} to ${filePath} using shell command`
        )

        this.#logger.info(`Successfully applied chmod ${chmod} to ${filePath} using shell command`)
      } catch (shellError) {
        this.#logger.error(`Shell chmod command failed: ${shellError.message}`)
        throw new DockerContainerError(`Failed to apply chmod ${chmod} to ${filePath}: ${shellError.message}`, this.#container?.id, shellError)
      }
    }
  }

  /**
   * Forward the exec method to the container
   *
   * @param {Object} options - Exec options
   * @returns {Promise<Object>} Exec instance
   */
  exec(options) {
    return this.#container.exec(options)
  }

  /**
   * Forward the putArchive method to the container
   *
   * @param {stream.Readable} tarStream - Tar stream to upload
   * @param {Object} options - Options for putArchive
   * @returns {Promise<Object>} Result
   */
  putArchive(tarStream, options) {
    return this.#container.putArchive(tarStream, options)
  }

  /**
   * Forward the start method to the container
   *
   * @param {Object} options - Start options
   * @returns {Promise<Object>} Result
   */
  start(options) {
    return this.#container.start(options)
  }

  /**
   * Forward the stop method to the container
   *
   * @param {Object} options - Stop options
   * @returns {Promise<Object>} Result
   */
  stop(options) {
    return this.#container.stop(options)
  }

  /**
   * Forward the remove method to the container
   *
   * @param {Object} options - Remove options
   * @returns {Promise<Object>} Result
   */
  remove(options) {
    return this.#container.remove(options)
  }

  /**
   * Forward the inspect method to the container
   *
   * @returns {Promise<Object>} Container info
   */
  inspect() {
    return this.#container.inspect()
  }

  /**
   * Get the container ID
   *
   * @returns {string} Container ID
   */
  get id() {
    return this.#container.id
  }

  /**
   * Expands the buffer if needed to accommodate new data
   *
   * @param {Buffer} buffer - Current buffer
   * @param {number} bufferLength - Current buffer length
   * @param {Buffer} chunk - New data chunk
   * @returns {[Buffer, number]} Updated buffer and length
   * @private
   */
  #expandBufferIfNeeded(buffer, bufferLength, chunk) {
    const requiredLength = bufferLength + chunk.length
    if (requiredLength > buffer.length) {
      const newBuffer = Buffer.allocUnsafe(Math.max(requiredLength, buffer.length * 2))
      buffer.copy(newBuffer)
      buffer = newBuffer
    }
    chunk.copy(buffer, bufferLength)
    bufferLength += chunk.length

    return [buffer, bufferLength]
  }

  /**
   * Process Docker stream buffer data and extract the output
   *
   * @param {Buffer} buffer - Buffer containing Docker stream data
   * @returns {string} Processed output from the stream
   * @private
   */
  #demuxStreamData(buffer) {
    let offset = 0
    let output = ''

    while (offset < buffer.length) {
      const streamType = buffer.readUInt8(offset) // 1 = stdout, 2 = stderr
      const payloadLength = buffer.readUInt32BE(offset + 4)
      const payload = buffer.subarray(offset + 8, offset + 8 + payloadLength).toString('utf8')

      if (streamType === 1) {
        output += payload
      } else if (streamType === 2) {
        const lines = payload.split('\n')
        for (const line of lines) {
          if (line.trim() !== '') {
            output += `${line}\n`
          }
        }
      }

      offset += 8 + payloadLength
    }

    return output
  }
}
