<?php

declare(strict_types=1);

namespace Imponeer\QueueInteropConnectionFactoryHelper;

use Enqueue\AmqpBunny\AmqpConnectionFactory as AmqpBunnyConnectionFactory;
use Enqueue\AmqpExt\AmqpConnectionFactory as AmqpExtConnectionFactory;
use Enqueue\AmqpLib\AmqpConnectionFactory as AmqpLibConnectionFactory;
use Enqueue\Dbal\DbalConnectionFactory;
use Enqueue\Fs\FsConnectionFactory;
use Enqueue\Gearman\GearmanConnectionFactory;
use Enqueue\Gps\GpsConnectionFactory;
use Enqueue\Mongodb\MongodbConnectionFactory;
use Enqueue\Null\NullConnectionFactory;
use Enqueue\Pheanstalk\PheanstalkConnectionFactory;
use Enqueue\RdKafka\RdKafkaConnectionFactory;
use Enqueue\Redis\RedisConnectionFactory;
use Enqueue\Sqs\SqsConnectionFactory;
use Enqueue\Stomp\StompConnectionFactory;
use Enqueue\Wamp\WampConnectionFactory;
use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\DSNNotSupportedException;
use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\EmptyDSNException;
use Interop\Queue\ConnectionFactory;
use Interop\Queue\Context;

/**
 * Queue-Interop factory helper
 *
 * @package Imponeer\QueueInteropConnectionFactoryHelper
 */
final class QueueConnectionFactoryHelper
{

    /**
     * Disabled constructor - this is a utility class
     */
    private function __construct()
    {
        // Utility class - no instantiation allowed
    }

    /**
     * Get connection factory based on DSN
     *
     * @param string $dsn DSN string to make connection
     *
     * @return ConnectionFactory
     *
     * @throws DSNNotSupportedException When the DSN driver is not supported
     * @throws EmptyDSNException When the DSN is empty
     */
    public static function getFactory(string $dsn): ConnectionFactory
    {
        $dsn = trim($dsn);

        if (empty($dsn)) {
            throw new EmptyDSNException();
        }

        $driver = strstr($dsn, ':', true);
        if ($driver === false) {
            throw new DSNNotSupportedException('Invalid DSN format');
        }

        return match ($driver) {
            'amqp' => self::createAmqpConnectionFactory($dsn),
            'stomp' => new StompConnectionFactory($dsn),
            'gps' => new GpsConnectionFactory($dsn),
            'gearman' => new GearmanConnectionFactory($dsn),
            'null' => new NullConnectionFactory(),
            'file' => new FsConnectionFactory($dsn),
            'beanstalk' => new PheanstalkConnectionFactory($dsn),
            'sqs' => new SqsConnectionFactory($dsn),
            'kafka' => new RdKafkaConnectionFactory($dsn),
            'redis' => new RedisConnectionFactory($dsn),
            'mongodb' => new MongodbConnectionFactory($dsn),
            'mysql' => new DbalConnectionFactory($dsn),
            'wamp', 'ws' => new WampConnectionFactory($dsn),
            default => throw new DSNNotSupportedException($driver),
        };
    }

    /**
     * Create AMQP connection factory with fallback logic
     *
     * @param string $dsn DSN string
     *
	 * @return ConnectionFactory
	 *
	 * @noinspection PhpUndefinedClassInspection
	 * @noinspection PhpIncompatibleReturnTypeInspection
	 */
    private static function createAmqpConnectionFactory(string $dsn): ConnectionFactory
    {
        if (class_exists(AmqpExtConnectionFactory::class) && extension_loaded('amqp')) {
            return new AmqpExtConnectionFactory($dsn);
        }

        if (class_exists(AmqpLibConnectionFactory::class)) {
            return new AmqpLibConnectionFactory($dsn);
        }

        return new AmqpBunnyConnectionFactory($dsn);
    }

    /**
     * Create context from DSN
     *
     * @param string $dsn Connection string
     *
     * @return Context
     *
     * @throws DSNNotSupportedException When the DSN driver is not supported
     * @throws EmptyDSNException When the DSN is empty
     */
    public static function createContext(string $dsn): Context
    {
        return self::getFactory($dsn)->createContext();
    }
}
