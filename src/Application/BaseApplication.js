/**
 * Base application class for both the main action and CLI scripts
 *
 * This abstract class provides common functionality for initializing the container
 * and running the application. It is meant to be extended by both the main action
 * and CLI scripts.
 *
 * @module Base/BaseApplication
 */

import path from 'path'
import { ContainerBuilder, YamlFileLoader } from 'node-dependency-injection'
import { setContainer as setComposerPlatformResolverContainer } from '../Resolvers/ComposerPlatformResolver.js'

/**
 * Abstract base class for applications
 *
 * This class should not be instantiated directly. Instead, use the static create() method.
 */
export default class BaseApplication {
  /**
   * @type {ContainerBuilder}
   * @protected
   */
  #container;

  /**
   * @type {boolean}
   * @private
   */
  static #instanceCreated = false;

  /**
   * Constructor
   *
   * @protected
   */
  constructor() {
    if (!BaseApplication.#instanceCreated) {
      throw new Error('Applications should be created using the static execute() method');
    }
    BaseApplication.#instanceCreated = false;
  }

  /**
   * Initialize the container with the services.yaml configuration
   *
   * @param {boolean} [autowire=true] - Whether to enable autowiring
   * @param {string} [srcDir] - The source directory for autowiring
   * @returns {Promise<ContainerBuilder>} The initialized container
   * @protected
   */
  async initializeContainer(autowire = true, srcDir = null) {
    const rootDir = process.cwd()
    const configPath = path.join(rootDir, 'src', 'Configs')

    // Create container
    const container = new ContainerBuilder(autowire, srcDir || path.join(rootDir, 'src'))

    // Set parameters
    container.setParameter('root_path', rootDir)
    container.setParameter('config_path', configPath)
    container.setParameter('fixtures_path', rootDir + '/tests/fixtures/composer-projects')
    container.setParameter('php_versions_config_path', configPath + '/php-versions.yaml')
    container.setParameter('composer_versions_config_path', configPath + '/composer-versions.yaml')

    // Load services configuration
    const loader = new YamlFileLoader(container)
    await loader.load(configPath + '/services.yaml')

    await container.compile()

    this.#container = container

    // Set container for resolvers that need it
    setComposerPlatformResolverContainer(container)

    return container
  }

  /**
   * Run the application
   * This method must be implemented by subclasses
   *
   * @returns {Promise<void>}
   * @abstract
   */
  async run() {
    throw new Error('The run method must be implemented by subclasses')
  }

  /**
   * Execute the application
   * This method boots the application and then runs it
   *
   * @returns {Promise<void>}
   */
  static async execute() {
    BaseApplication.#instanceCreated = true;

    const app = new this()
    try {
      await app.initializeContainer()
      await app.run()
    } catch (error) {
      app._get('logger').error(error.message)
      process.exit(1)
    }
  }

  /**
   * Get a service from the container
   *
   * @param {string} service The service to get
   * @returns {any} The service
   *
   * @protected
   */
  _get(service) {
    return this.#container.get(service)
  }

  /**
   * Get a parameter from the container
   *
   * @param {string} parameterName The parameter to get
   *
   * @returns {string}
   *
   * @protected
   */
  _getParameter(parameterName) {
     return this.#container.getParameter(parameterName)
  }
}
