<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Models\DailyTask;
use App\Models\Games\Game;
use App\Models\Lobbies\Lobby;
use App\Models\MatchmakingAd;
use App\Models\User;
use App\Models\UserDailyTask;
use App\Services\DailyTaskService;
use Carbon\Carbon;
use Tests\TestCaseWithAuth;
use Tests\Traits\AssertMath;

class DailyTaskServiceTest extends TestCaseWithAuth
{
    use AssertMath;

    private const EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1 = 1500;

    private const TASK_TYPES = [
        DailyTask::TYPE_CREATE_A_MATCHMAKING_AD,
        DailyTask::TYPE_CREATE_A_POST,
        DailyTask::TYPE_INVITE_A_FRIEND,
        DailyTask::TYPE_SHARE_A_POST,
    ];

    private const MIN_TASK_COUNT = 1;
    private const MAX_TASK_COUNT = 3;

    private const MIN_SHARD_REWARD = 1;
    private const MAX_SHARD_REWARD = 3;

    public function testDailyTestRegeneration(): void
    {
        $user = User::factory()->createOne();

        for ($i = 1; $i <= 5; ++$i) {
            $user->level = $i;
            $user->save();

            /** @var DailyTaskService $dailyTaskService */
            $dailyTaskService = app(DailyTaskService::class);

            $dailyTaskService->regenerateTasksForUser($user, 4);

            $generatedTasks = UserDailyTask::all()->toArray();

            $this->assertCount(4, $generatedTasks);

            foreach ($generatedTasks as $generatedTask) {
                $this->assertContains($generatedTask['type'], self::TASK_TYPES);
                $this->assertSame(0, $generatedTask['count_progress']);
                $this->assertBetweenInclusive(self::MIN_TASK_COUNT, self::MAX_TASK_COUNT, $generatedTask['count_target']);
                $this->assertSame(0, $generatedTask['is_complete']);
                $this->assertBetweenInclusive(0, 1, $generatedTask['is_repeatable']);
                $this->assertBetweenInclusive(self::MIN_SHARD_REWARD, self::MAX_SHARD_REWARD, $generatedTask['shard_reward']);
            }
        }
    }

    public function testSimpleCreateAPostDailyTask(): void
    {
        $user = User::factory()->createOne();
        $userTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_POST,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 1,
        ]);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 1',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 2',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);
    }

    public function testSimpleShareAPostDailyTask(): void
    {
        $user = User::factory()->createOne();
        $user2 = User::factory()->createOne();
        $createAPostTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_POST,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 1,
        ]);
        $shareAPostTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_SHARE_A_POST,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 2,
        ]);

        $postId = $this->actingAs($user2, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 1',
                ]
            )->json('data.id');

        $this->actingAs($user, 'api')
            ->post(
                route(
                    'api.v1.post.share',
                    [
                        'postId' => $postId,
                    ]
                ),
                [
                    'message' => 'Shared test post 1',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $createAPostTask->refresh();
        $shareAPostTask->refresh();
        $user->refresh();

        $this->assertSame(0, $createAPostTask->count_progress);
        $this->assertSame(0, $createAPostTask->is_complete);
        $this->assertSame(1, $shareAPostTask->count_progress);
        $this->assertSame(1, $shareAPostTask->is_complete);
        $this->assertSame(2, $user->shards);
        $this->assertSame(0, $user2->shards);
        $this->assertEquals(0, $user->experience);

        $this->actingAs($user, 'api')
            ->post(
                route(
                    'api.v1.post.share',
                    [
                        'postId' => $postId,
                    ]
                ),
                [
                    'message' => 'Shared test post 2',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $createAPostTask->refresh();
        $shareAPostTask->refresh();
        $user->refresh();

        $this->assertSame(0, $createAPostTask->count_progress);
        $this->assertSame(0, $createAPostTask->is_complete);
        $this->assertSame(1, $shareAPostTask->count_progress);
        $this->assertSame(1, $shareAPostTask->is_complete);
        $this->assertSame(2, $user->shards);
        $this->assertSame(0, $user2->shards);
        $this->assertEquals(0, $user->experience);
    }

    public function testDailyTaskReward(): void
    {
        $user = User::factory()->createOne();
        $user2 = User::factory()->createOne();
        $createAPostTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_POST,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 1,
        ]);
        $shareAPostTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_SHARE_A_POST,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 2,
        ]);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 1',
                ]
            )->json('data.id');

        $createAPostTask->refresh();
        $shareAPostTask->refresh();
        $user->refresh();

        $this->assertSame(1, $createAPostTask->count_progress);
        $this->assertSame(1, $createAPostTask->is_complete);
        $this->assertSame(0, $shareAPostTask->count_progress);
        $this->assertSame(0, $shareAPostTask->is_complete);
        $this->assertSame(1, $user->shards);
        $this->assertSame(0, $user2->shards);
        $this->assertEquals(0, $user->experience);

        $postId = $this->actingAs($user2, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 2',
                ]
            )->json('data.id');

        $this->actingAs($user, 'api')
            ->post(
                route(
                    'api.v1.post.share',
                    [
                        'postId' => $postId,
                    ]
                ),
                [
                    'message' => 'Shared test post 1',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $createAPostTask->refresh();
        $shareAPostTask->refresh();
        $user->refresh();

        $this->assertSame(1, $createAPostTask->count_progress);
        $this->assertSame(1, $createAPostTask->is_complete);
        $this->assertSame(1, $shareAPostTask->count_progress);
        $this->assertSame(1, $shareAPostTask->is_complete);
        $this->assertSame(3, $user->shards);
        $this->assertSame(0, $user2->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);

        $this->actingAs($user, 'api')
            ->post(
                route(
                    'api.v1.post.share',
                    [
                        'postId' => $postId,
                    ]
                ),
                [
                    'message' => 'Shared test post 2',
                ]
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $createAPostTask->refresh();
        $shareAPostTask->refresh();
        $user->refresh();

        $this->assertSame(1, $createAPostTask->count_progress);
        $this->assertSame(1, $createAPostTask->is_complete);
        $this->assertSame(1, $shareAPostTask->count_progress);
        $this->assertSame(1, $shareAPostTask->is_complete);
        $this->assertSame(3, $user->shards);
        $this->assertSame(0, $user2->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);
    }

    public function testSimpleInviteAFriendDailyTask(): void
    {
        $user = User::factory()->createOne();
        $notYetAFriend = User::factory()->createOne();
        $anotherNotYetAFriend = User::factory()->createOne();
        $userTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_INVITE_A_FRIEND,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 2,
        ]);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.friends.invitation.invite', [
                    'userId' => $notYetAFriend->getRouteKey(),
                ])
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertSame(2, $user->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.friends.invitation.invite', [
                    'userId' => $anotherNotYetAFriend->getRouteKey(),
                ])
            )
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertSame(2, $user->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);
    }

    public function testSimpleCreateAMatchmakingAdDailyTask(): void
    {
        /**
         * @var User $user
         */
        $user = User::factory()->createOne();
        $userTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_MATCHMAKING_AD,
            'count_target' => 1,
            'is_repeatable' => 0,
            'shard_reward' => 3,
        ]);

        $game = Game::first();
        $gameId = $game->id;

        $payload = Lobby::factory()->generateLobbyCreateControllerArguments();
        $payload['start_date'] = date(
            MatchmakingAd::DATE_FORMAT,
            mktime(12, 0, 0, 05, 10,   Carbon::now()->addYear()->year)
        );
        $payload['end_date'] = date(
            MatchmakingAd::DATE_FORMAT,
            mktime(14, 0, 0, 05, 10,   Carbon::now()->addYear()->year)
        );

        $response = $this->actingAs($user, 'api')
            ->post(route('api.v1.matchmaking_ads.create'), $payload);

        $response
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertSame(3, $user->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);

        $this->actingAs($user, 'api')
            ->post(route('api.v1.matchmaking_ads.create'), $payload)
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertSame(3, $user->shards);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);
    }

    public function testTripleCreateAPostDailyTask(): void
    {
        $user = User::factory()->createOne();
        $userTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_POST,
            'count_target' => 3,
            'is_repeatable' => 0,
            'shard_reward' => 5,
        ]);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 1',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(0, $userTask->is_complete);
        $this->assertEquals(0, $user->experience);
        $this->assertEquals(0, $user->shards);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 2',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(2, $userTask->count_progress);
        $this->assertSame(0, $userTask->is_complete);
        $this->assertEquals(0, $user->experience);
        $this->assertEquals(0, $user->shards);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 3',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(3, $userTask->count_progress);
        $this->assertSame(1, $userTask->is_complete);
        $this->assertEquals(self::EXPERIENCE_FOR_DAILY_TASKS_AT_LEVEL_1, $user->experience);
        $this->assertSame(2, $user->level);
        $this->assertEquals(5, $user->shards);
    }

    public function testCreateAPostDailyTaskRepeatable(): void
    {
        $user = User::factory()->createOne();
        $userTask = UserDailyTask::create([
            'user_id' => $user->id,
            'type' => DailyTask::TYPE_CREATE_A_POST,
            'count_target' => 2,
            'is_repeatable' => 1,
            'shard_reward' => 5,
        ]);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 1',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(0, $userTask->is_complete);
        $this->assertEquals(0, $user->experience);
        $this->assertEquals(0, $user->shards);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 2',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(0, $userTask->count_progress);
        $this->assertSame(0, $userTask->is_complete);
        $this->assertEquals(0, $user->experience);
        $this->assertSame(1, $user->level);
        $this->assertEquals(5, $user->shards);

        $this->actingAs($user, 'api')
            ->post(
                route('api.v1.post.create'),
                [
                    'message' => 'Test post 3',
                ]
            );

        $userTask->refresh();
        $user->refresh();

        $this->assertSame(1, $userTask->count_progress);
        $this->assertSame(0, $userTask->is_complete);
        $this->assertEquals(0, $user->experience);
        $this->assertSame(1, $user->level);
        $this->assertEquals(5, $user->shards);
    }
}
