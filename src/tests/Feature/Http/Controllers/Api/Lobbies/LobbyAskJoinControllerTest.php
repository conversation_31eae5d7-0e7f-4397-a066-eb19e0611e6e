<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\Api\Lobbies;

use App\Models\Lobbies\Lobby;
use App\Models\Messenger\Room;
use App\Models\User;
use App\Services\Helper;
use Tests\TestCase;

class LobbyAskJoinControllerTest extends TestCase
{
    public function testCreateAskJoin(): void
    {
        $user = User::factory()->createOne();
        $user2 = User::factory()->createOne();
        /** @var User */
        $user3 = User::factory()->createOne();

        /** @var Lobby $lobby */
        $lobby = Lobby::factory()->createOneWithModerators([$user, $user2], false);
        $chatRoom = Room::factory()->createOneWithParticipants([$user, $user2]);
        $lobby->_chat_room_id = $chatRoom->id;
        $lobby->save();

        $this->assertSame(
            $lobby->askedjoin_user_ids,
            null
        );

        $this->actingAs($user3, 'api')
            ->post(route('api.v1.lobbies.item.joinasks.create', ['lobbyId' => $lobby->id]))
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $lobby->refresh();
        $this->assertSame(
            $lobby->askedjoin_user_ids,
            [
                $user3->id,
            ]
        );

        $this->actingAs($user3, 'api')
            ->post(route('api.v1.lobbies.item.joinasks.create', ['lobbyId' => $lobby->id]))
            ->assertStatus(400)
            ->assertJsonPath('success', false);

        $this->actingAs($user3, 'api')
            ->post(route('api.v1.lobbies.item.joinasks.create', ['lobbyId' => 'wrongId']))
            ->assertStatus(404)
            ->assertJsonPath('success', false);
    }

    public function testAcceptAskJoin(): void
    {
        /** @var User $user */
        $user = User::factory()->createOne();
        /** @var User $user2 */
        $user2 = User::factory()->createOne();
        /** @var User $user3 */
        $user3 = User::factory()->createOne();

        $lobby = Lobby::factory()->createOneWithModerators([$user, $user2], false);
        $chatRoom = Room::factory()->createOneWithParticipants([$user, $user2]);
        $lobby->_chat_room_id = $chatRoom->id;
        $lobby->askedjoin_user_ids = [
            $user3->id,
        ];
        $lobby->save();

        $this->actingAs($user, 'api')
            ->post(route('api.v1.lobbies.item.joinasks.accept', ['lobbyId' => $lobby->id, 'userId' => Helper::encodeHashId($user3->id, User::class)]))
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $lobby->refresh();
        $this->assertSame(
            $lobby->askedjoin_user_ids,
            []
        );
        $this->assertSame(
            $lobby->user_ids,
            [
                $user->id,
                $user2->id,
                $user3->id,
            ]
        );
    }

    public function testDeclineAskJoin(): void
    {
        /** @var User $user */
        $user = User::factory()->createOne();
        $user2 = User::factory()->createOne();
        $user3 = User::factory()->createOne();

        $lobby = Lobby::factory()->createOneWithModerators([$user, $user2], false);
        $chatRoom = Room::factory()->createOneWithParticipants([$user, $user2]);
        $lobby->_chat_room_id = $chatRoom->id;
        $lobby->askedjoin_user_ids = [
            $user3->id,
        ];
        $lobby->save();

        $this->actingAs($user, 'api')
            ->post(route('api.v1.lobbies.item.joinasks.decline', ['lobbyId' => $lobby->id, 'userId' => Helper::encodeHashId($user3->id, User::class)]))
            ->assertStatus(200)
            ->assertJsonPath('success', true);

        $lobby->refresh();
        $this->assertSame(
            $lobby->askedjoin_user_ids,
            []
        );
        $this->assertSame(
            $lobby->user_ids,
            [
                $user->id,
                $user2->id,
            ]
        );
    }
}
