<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\Api;

use App\Models\Games\Game;
use App\Models\User;
use Tests\TestCase;

class SearchControllerTest extends TestCase
{
    public function testShouldBeUnprocessed(): void
    {
        /**
         * @var User $user
         */
        $user = User::factory()->create();

        $response = $this->actingAs($user, 'api')
            ->get(route('api.v1.search.search'));

        $response->assertUnprocessable();
        $response->assertJsonStructure([
            'errors' => [
                'query',
            ]
        ]);
    }

    public function testShouldReturnMultipleGamesAndUsers(): void
    {
        $resultCount = 2;
        $searchString = 'test';

        /**
         * @var User $user
         */
        $user = User::factory()->create();

        User::factory()->create([
            'nickname' => 'testing user'
        ]);
        User::factory()->create([
            'nickname' => 'Test user'
        ]);
        Game::factory()->create([
            'name' => 'testing game',
        ]);
        Game::factory()->create([
            'name' => 'Test game',
        ]);

        $response = $this->actingAs($user, 'api')
            ->get(
                sprintf(
                    '%s?query=%s',
                    route('api.v1.search.search'),
                    $searchString
                )
            );

        $response->assertSuccessful();
        $response->assertJsonCount($resultCount, 'data');
    }

    public function testShouldReturnLimitedGamesAndUsers(): void
    {
        $limit = 4;
        $resultCount = $limit;
        $searchString = 'test';

        /**
         * @var User $user
         */
        $user = User::factory()->create();

        User::factory()->count(10)->createWithNicknamePrefix($searchString);
        Game::factory()->count(10)->create([
            'name' => 'testing game',
        ]);

        $response = $this->actingAs($user, 'api')
            ->get(
                sprintf(
                    '%s?query=%s&limit=%s',
                    route('api.v1.search.search'),
                    $searchString,
                    $limit
                )
            );

        $response->assertSuccessful();
        $response->assertJsonCount($resultCount, 'data');
    }
}
