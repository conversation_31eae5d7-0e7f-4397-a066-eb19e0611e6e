<?php

namespace Tests\Feature\Http\Controllers\Api;

use App\Mail\ExternalInvitationMail;
use App\Models\User;
use App\Notifications\YourInvitationJustRegisteredNotification;
use App\Services\ExternalInvitationService;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Tests\TestCaseWithAuth;

class ExternalInvitationControllerTest extends TestCaseWithAuth
{
    private const TEST_USERNAME = 'LeetHaxor';
    private const TEST_EMAIL = '<EMAIL>';
    private const TEST_COMPANY_EMAIL = '<EMAIL>';
    private const TEST_PASSWORD = 'hackerMan1337';

    public function testExternalInvitationFromUser(): void {
        $user1 = User::factory()->create();

        Mail::fake();

        $invitationRequestData = [
            route('api.v1.auth.invitation.invite'),
            [
                'email' => self::TEST_COMPANY_EMAIL,
            ]
        ];

        for ($i = 0; $i < 3; $i++) {
            $response = $this->actingAs($user1, 'api')
                ->withoutMiddleware(ThrottleRequests::class)
                ->post(...$invitationRequestData);

            $response->assertSuccessful();
            $response->assertJsonStructure([
                'data' => [
                    'code'
                ]
            ]);

            $this->assertEquals(
                trans('auth.external_invitation.sent'),
                $response->json('message')
            );

            Mail::assertQueued(ExternalInvitationMail::class);
        }
    }

    public function testWithoutEmailStartAcceptingInvitation(): void {
        $code = app(ExternalInvitationService::class)->generateCodeBasedInvitation()->code;

        $response = $this
            ->withoutMiddleware(ThrottleRequests::class)
            ->get(
                route('api.v1.auth.invitation.start_to_accept', ['code' => $code])
            )
        ;

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'email',
                'code',
            ]
        ]);

        $this->assertNull($response['data']['email']);
        $this->assertNotNull($response['data']['code']);
        $this->assertSame($response['data']['code'], $code);

        $this->assertEquals(
            trans('auth.external_invitation.start_to_accept'),
            $response->json('message')
        );

        Notification::fake();
        Mail::fake();

        $this
            ->withoutMiddleware(ThrottleRequests::class)
            ->post(
                route('api.v1.auth.register'),
                [
                    'nickname' => self::TEST_USERNAME,
                    'email' => self::TEST_EMAIL,
                    'password' => self::TEST_PASSWORD,
                    'password_confirmation' => self::TEST_PASSWORD,
                    'code' => $code,
                    'gdpr' => true,
                ]
            )
            ->assertSuccessful();
    }

    public function testWithEmailInvitation(): void {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        /**
         * @var ExternalInvitationService $externalInvitationService
         */
        $externalInvitationService = app(ExternalInvitationService::class);
        $code = $externalInvitationService->inviteFromUser($user1, self::TEST_EMAIL)->code;
        $externalInvitationService->inviteFromUser($user2, self::TEST_EMAIL);

        $response = $this
            ->withoutMiddleware(ThrottleRequests::class)
            ->get(
                route('api.v1.auth.invitation.start_to_accept', ['code' => $code])
            )
        ;

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'email',
                'code',
            ]
        ]);

        $this->assertSame($response['data']['code'], $code);
        $this->assertSame($response['data']['email'], self::TEST_EMAIL);

        $this->assertEquals(
            trans('auth.external_invitation.start_to_accept'),
            $response->json('message')
        );

        Notification::fake();
        Mail::fake();

        $this
            ->withoutMiddleware(ThrottleRequests::class)
            ->post(
                route('api.v1.auth.register'),
                [
                    'nickname' => self::TEST_USERNAME,
                    'email' => self::TEST_EMAIL,
                    'password' => self::TEST_PASSWORD,
                    'password_confirmation' => self::TEST_PASSWORD,
                    'code' => $code,
                    'gdpr' => true,
                ]
            )
            ->assertSuccessful();

        Notification::assertSentTo($user1, YourInvitationJustRegisteredNotification::class);
        Notification::assertSentTo($user2, YourInvitationJustRegisteredNotification::class);

        $this->assertNotNull($user1->friends->first());

        $newFriend = $user1
            ->friends
            ->first()
            ->friendUser;
        $this->assertNotNull($newFriend);
        $this->assertSame(self::TEST_EMAIL, $newFriend->email);
    }
}
