import mime from 'mime-types'
import {readFileSync} from 'fs'
import file<PERSON><PERSON><PERSON><PERSON><PERSON> from 'file-type-checker'
import {extname} from 'path'

export default class Filesystem {
  /**
   * Detects the MIME type of file
   *
   * @param {string} filename - The path to the file
   * @returns {string} The MIME type of the file
   */
  static detectMimeType(filename) {
    try {
      const buffer = readFileSync(filename)
      const fileType = fileTypeChecker.detectFile(buffer)

      if (fileType && fileType.mimeType) {
        return fileType.mimeType
      }

      // eslint-disable-next-line no-unused-vars
    } catch (error) {
      // ignore error
    }

    const mimeType = mime.lookup(filename)
    return mimeType || 'application/octet-stream'
  }

  static getExtension(filename) {
    let extension = extname(filename).substring(1) || null
    if (extension && (extension.includes('?') || extension.includes('#'))) {
      extension = null
    }
  }

}