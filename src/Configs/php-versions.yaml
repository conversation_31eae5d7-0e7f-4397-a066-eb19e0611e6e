# PHP versions configuration file
#
# This file contains information about PHP versions and their support status.
#
# WARNING: DO NOT EDIT THIS FILE DIRECTLY!
# This file is automatically generated by the update-versions script.
# To update, run: npm run update-versions

# The constraint for the currently minimum supported PHP version
LATEST_SUPPORTED_PHP_VERSION: ">=8.3"

# ALL_PHP_VERSIONS contains a list of all available PHP versions (major.minor only)
# with their support status. Possible status values: 'ACTIVE', 'SECURITY', 'EOL',
# or custom status.
ALL_PHP_VERSIONS:
  "8.4": ACTIVE
  "8.3": ACTIVE
  "8.2": SECURITY
  "8.1": SECURITY
  "8.0": EOL
  "7.4": EOL
  "7.3": EOL
  "7.2": EOL
  "7.1": EOL
  "7.0": EOL
  "5.6": EOL
  "5.5": EOL
  "5.4": EOL
