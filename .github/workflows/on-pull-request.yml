name: On pull request

on:
  pull_request:
    branches:
      - main

permissions: write-all

jobs:
  tests:
    runs-on: ubuntu-latest
    continue-on-error: false
    strategy:
      max-parallel: 3
      matrix:
        php:
          - 8.3
          - 8.4
        composer:
          - 2
    name: Test - php:${{ matrix.php }}; composer:${{ matrix.composer }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install PHP
        uses: shivammathur/setup-php@2.33.0
        with:
          php-version: ${{ matrix.php }}
          extensions: curl, gd, pdo_mysql, json, mbstring, pcre, session
          ini-values: post_max_size=256M
          coverage: none
          tools: composer:v${{ matrix.composer }}
      - name: Composer validate
        run: composer validate --strict --no-check-lock
      - name: Install Composer dependencies (with dev)
        run: composer install --no-progress --no-suggest --prefer-dist --optimize-autoloader
      - name: Tests
        run: ./vendor/bin/phpunit
      - name: Install Composer dependencies (without dev)
        run: composer install --no-progress --no-dev --no-suggest --prefer-dist --optimize-autoloader

  dependabot:
    needs: tests
    runs-on: ubuntu-latest
    # Checking the actor will prevent your Action run failing on non-Dependabot
    # PRs but also ensures that it only does work for Dependabot PRs.
    if: ${{ github.actor == 'dependabot[bot]' }}
    steps:
      # This first step will fail if there's no metadata and so the approval
      # will not occur.
      - name: Dependabot metadata
        id: dependabot-metadata
        uses: dependabot/fetch-metadata@v2.4.0
        with:
          github-token: "${{ secrets.GITHUB_TOKEN }}"
      # Here the PR gets approved.
      - name: Approve a PR
        run: gh pr review --approve "$PR_URL"
        env:
          PR_URL: ${{ github.event.pull_request.html_url }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      # Finally, this sets the PR to allow auto-merging for patch and minor
      # updates if all checks pass
      - name: Enable auto-merge for Dependabot PRs
        # if: ${{ steps.dependabot-metadata.outputs.update-type != 'version-update:semver-major' }}
        run: gh pr merge --auto --squash "$PR_URL"
        env:
          PR_URL: ${{ github.event.pull_request.html_url }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
