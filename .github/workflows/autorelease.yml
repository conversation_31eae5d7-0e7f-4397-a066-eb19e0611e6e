name: Automatic releases

on:
  workflow_dispatch:
  schedule:
    - cron: '5 4 * */3 0'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  auto-release:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/

      - name: Releasing if there is something new...
        uses: impresscms-dev/simple-autorelease-action@v0.2
        with:
          release_branch: main
          github_token: ${{ secrets.GITHUB_TOKEN }}
          default_bump: patch
