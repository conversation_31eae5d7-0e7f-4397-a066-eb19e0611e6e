{"name": "imponeer/smarty-debug", "description": "Smarty plugin to debug template data", "type": "library", "require": {"symfony/var-dumper": "^5.2", "smarty/smarty": "^5.0", "php": "^8.3"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Smarty\\Extensions\\Debug\\": "src/"}}, "autoload-dev": {"psr-4": {"Imponeer\\Smarty\\Extensions\\Debug\\Tests\\": "tests/"}}, "keywords": ["smarty", "symfony", "var-dumper", "debug", "smarty-plugins"], "require-dev": {"phpunit/phpunit": "^10.0", "squizlabs/php_codesniffer": "^3.8", "phpstan/phpstan": "^2"}, "scripts": {"test": "phpunit --testdox", "phpcs": "phpcs", "phpcbf": "phpcbf", "phpstan": "phpstan analyse"}}