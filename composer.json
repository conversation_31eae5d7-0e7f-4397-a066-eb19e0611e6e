{"name": "imponeer/queue-interop-connection-factory-helper", "description": "Helper that creates queue-interop connection factory based on DSN", "type": "library", "license": "MIT", "minimum-stability": "dev", "prefer-stable": true, "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.3", "queue-interop/queue-interop": "^0.8"}, "require-dev": {"enqueue/amqp-bunny": "^0.10.26", "enqueue/amqp-lib": "^0.10.26", "enqueue/dbal": "^0.10.26", "enqueue/fs": "^0.10.26", "enqueue/gps": "^0.10.26", "enqueue/null": "^0.10.26", "enqueue/pheanstalk": "^0.10.26", "enqueue/redis": "^0.10.26", "enqueue/sqs": "^0.10.26", "enqueue/wamp": "^0.10.26", "jchook/phpunit-assert-throws": "^1.0", "phpunit/phpunit": "^10.0"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "autoload": {"psr-4": {"Imponeer\\QueueInteropConnectionFactoryHelper\\": "src/"}}}