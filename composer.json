{"name": "imponeer/smarty-foreachq", "description": "Rewritten Smarty foreach variant that was invented for use in xoops, but nowadays is used in some other PHP based CMS'es", "type": "library", "require": {"imponeer/smarty-extensions-contracts": "^1.0 || ^2.0", "php": ">=7.1"}, "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Imponeer\\Smarty\\Extensions\\ForeachQ\\": "src/"}}, "keywords": ["smarty", "xoops", "foreach", "smarty-plugins"]}