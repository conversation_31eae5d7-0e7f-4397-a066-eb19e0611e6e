import path from 'path'
import { fileURLToPath } from 'url'
import { exec } from 'child_process'
import { promisify } from 'util'
import YamlService from '../../../src/Services/YamlService.js'
import fs from 'fs'

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url))
const rootDir = path.resolve(__dirname, '../../../')
const execAsync = promisify(exec)

describe('GetPhpVersionApplication Integration Test', () => {
  test('should output the same value as LATEST_SUPPORTED_PHP_VERSION from config', async () => {
    // Create a YamlService instance to read the config file
    const logger = {
      info: () => {},
      warn: () => {},
      error: () => {},
      debug: () => {}
    }
    const yamlService = new YamlService(logger, fs, path)

    // Read the actual config file to get the expected value
    const configPath = path.join(rootDir, 'src', 'Configs', 'php-versions.yaml')
    const phpVersionsConfig = yamlService.readYamlFile(configPath, {})
    const expectedVersion = phpVersionsConfig.LATEST_SUPPORTED_PHP_VERSION || ''

    // Execute the bin script directly
    const { stdout } = await execAsync('node bin/get-php-version.js')

    // Verify the output
    expect(stdout.trim()).toBe(expectedVersion)
  })
})
