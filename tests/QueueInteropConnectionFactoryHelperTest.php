<?php

declare(strict_types=1);

namespace Imponeer\QueueInteropConnectionFactoryHelper\Tests;

use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\DSNNotSupportedException;
use Imponeer\QueueInteropConnectionFactoryHelper\Exceptions\EmptyDSNException;
use Imponeer\QueueInteropConnectionFactoryHelper\QueueConnectionFactoryHelper;
use Jchook\AssertThrows\AssertThrows;
use PHPUnit\Framework\TestCase;

/**
 * Test QueueInteropConnectionFactoryHelper class
 *
 * @package Imponeer\QueueInteropConnectionFactoryHelper\Tests
 */
final class QueueInteropConnectionFactoryHelperTest extends TestCase
{

    use AssertThrows;

    /**
     * Test if required static methods exists
     */
    public function testStaticMethodsExists(): void
    {
        $requiredMethods = ['getFactory', 'createContext'];

        foreach ($requiredMethods as $method) {
            self::assertTrue(
                method_exists(QueueConnectionFactoryHelper::class, $method),
                "Method '{$method}' does not exist in " . QueueConnectionFactoryHelper::class
            );
        }
    }

    /**
     * Test that all registered DNS'es resolves to something
     */
    public function testIfGoodDNSIsResolvingCorrectly(): void
    {
        $dsnPrefixesToTest = [
            'amqp',
            'stomp',
            'gps',
            'gearman',
            'null',
            'file',
            'beanstalk',
            'sqs',
            'kafka',
            'redis',
            'mongodb',
            'mysql',
            'wamp',
            'ws',
        ];

        foreach ($dsnPrefixesToTest as $dsnPrefix) {
            $dsn = $dsnPrefix . ':';
            $this->assertThrows(
                \Error::class,
                fn() => QueueConnectionFactoryHelper::getFactory($dsn),
                static function (\Error $error): void {
                    self::assertMatchesRegularExpression(
                        '/Class "([^"]+)" not found|Class \'([^\']+)\' not found/',
                        $error->getMessage(),
                        'Unexpected error message format'
                    );
                }
            );
        }
    }

    /**
     * Test if unknown DSN throws DSNNotSupportedException
     */
    public function testIfUnknownDSNIsResolvingCorrectly(): void
    {
        $this->assertThrows(
            DSNNotSupportedException::class,
            fn() => QueueConnectionFactoryHelper::getFactory(sha1((string) microtime(true)) . ':')
        );
    }

    /**
     * Test if empty DSN throws EmptyDSNException
     */
    public function testEmptyDSNString(): void
    {
        $this->assertThrows(
            EmptyDSNException::class,
            fn() => QueueConnectionFactoryHelper::getFactory('')
        );

        $this->assertThrows(
            EmptyDSNException::class,
            fn() => QueueConnectionFactoryHelper::getFactory(' ')
        );
    }

}