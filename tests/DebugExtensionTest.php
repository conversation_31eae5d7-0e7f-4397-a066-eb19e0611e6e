<?php

use Imponeer\Smarty\Extensions\Debug\DebugExtension;
use PHPUnit\Framework\TestCase;

class DebugExtensionTest extends TestCase
{
    /**
     * @var DebugExtension
     */
    private $extension;

    protected function setUp(): void
    {
        $this->extension = new DebugExtension();
        parent::setUp();
    }

    public function testGetModifierCallback(): void
    {
        $callback = $this->extension->getModifierCallback('debug_print_var');
        $this->assertIsCallable($callback);
        
        // Test that it returns null for unknown modifiers
        $this->assertNull($this->extension->getModifierCallback('unknown_modifier'));
    }
}
