import { jest } from '@jest/globals'
import ConfigService from '../../src/Services/ConfigService.js'

describe('ConfigService', () => {
  let configService
  let mockYamlReader
  let composerVersionsConfigPath
  let phpVersionsConfigPath
  let mockComposerVersions
  let mockPhpVersions

  beforeEach(() => {
    // Set up mock data
    composerVersionsConfigPath = '/path/to/composer-versions.yaml'
    phpVersionsConfigPath = '/path/to/php-versions.yaml'
    
    mockComposerVersions = {
      COMPOSER_COMPATIBILITY_RULES: [
        { minPhpVersion: '8.2', composerVersion: '2.6' },
        { minPhpVersion: '8.0', composerVersion: '2.4' },
        { minPhpVersion: '7.4', composerVersion: '2.2' }
      ]
    }
    
    mockPhpVersions = {
      LATEST_SUPPORTED_PHP_VERSION: '>=8.3',
      ALL_PHP_VERSIONS: {
        '8.3': 'ACTIVE',
        '8.2': 'SECURITY',
        '8.1': 'SECURITY',
        '8.0': 'EOL'
      }
    }
    
    // Create mock YamlReader
    mockYamlReader = {
      readYamlFile: jest.fn()
    }
    
    // Set up mock return values
    mockYamlReader.readYamlFile
      .mockReturnValueOnce(mockComposerVersions) // First call for composer versions
      .mockReturnValueOnce(mockPhpVersions)      // Second call for PHP versions
    
    // Create a new instance of ConfigService with the mock YamlReader
    configService = new ConfigService(
      mockYamlReader,
      composerVersionsConfigPath,
      phpVersionsConfigPath
    )
  })

  test('constructor should read composer and PHP versions from YAML files', () => {
    expect(mockYamlReader.readYamlFile).toHaveBeenCalledTimes(2)
    expect(mockYamlReader.readYamlFile).toHaveBeenCalledWith(composerVersionsConfigPath, {})
    expect(mockYamlReader.readYamlFile).toHaveBeenCalledWith(phpVersionsConfigPath, {})
  })

  test('composerVersions getter should return composer versions data', () => {
    const result = configService.composerVersions
    expect(result).toBe(mockComposerVersions)
  })

  test('phpVersions getter should return PHP versions data', () => {
    const result = configService.phpVersions
    expect(result).toBe(mockPhpVersions)
  })

  test('should use empty object as default when YAML file cannot be read', () => {
    // Reset mocks
    mockYamlReader.readYamlFile.mockReset()
    
    // Set up mock to return undefined (file cannot be read)
    mockYamlReader.readYamlFile
      .mockReturnValueOnce(undefined) // First call for composer versions
      .mockReturnValueOnce(undefined) // Second call for PHP versions
    
    // Create a new instance of ConfigService
    const newConfigService = new ConfigService(
      mockYamlReader,
      composerVersionsConfigPath,
      phpVersionsConfigPath
    )
    
    // Check that empty objects are used as defaults
    expect(newConfigService.composerVersions).toEqual({})
    expect(newConfigService.phpVersions).toEqual({})
  })
})
