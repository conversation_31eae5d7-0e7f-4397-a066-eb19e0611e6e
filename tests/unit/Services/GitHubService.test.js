import GitHubService from '../../../src/Services/GitHubService.js'
import { jest } from '@jest/globals'

// Mock the Octokit constructor before importing the module
jest.mock('@octokit/rest', () => {
  return {
    Octokit: jest.fn().mockImplementation(() => ({
      repos: {
        getLatestRelease: jest.fn(),
        getReleaseByTag: jest.fn()
      }
    }))
  }
})

describe('GitHubService', () => {
  let githubService
  let mockLogger
  let mockFs
  let mockPath
  let mockContainer
  let mockOctokit

  beforeEach(() => {
    // Mock dependencies
    mockLogger = {
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }

    mockFs = {
      existsSync: jest.fn(),
      readFileSync: jest.fn()
    }

    mockPath = {
      join: jest.fn(),
      resolve: jest.fn()
    }

    // Mock DockerContainerDecorator
    mockContainer = {
      fileExistsInContainer: jest.fn(),
      executeContainerCommand: jest.fn(),
      exec: jest.fn(),
      inspect: jest.fn()
    }

    // Get the mocked Octokit instance
    mockOctokit = {
      repos: {
        getLatestRelease: jest.fn(),
        getReleaseByTag: jest.fn()
      }
    }

    // Create the service
    githubService = new GitHubService(mockLogger, mockFs, mockPath, 'test-token')

    // Replace the private octokit instance with our mock
    // Use defineProperty to access private field
    Object.defineProperty(githubService, '#octokit', {
      value: mockOctokit,
      writable: true
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('findLatestRepositoryRelease', () => {
    test('should return the latest release', async () => {
      // Mock data
      const owner = 'test-owner'
      const repo = 'test-repo'
      const mockRelease = {
        tag_name: 'v1.0.0',
        assets: []
      }

      // Setup mock
      mockOctokit.repos.getLatestRelease.mockResolvedValueOnce({
        data: mockRelease
      })

      // Call the method
      const result = await githubService.findLatestRepositoryRelease(owner, repo)

      // Verify
      expect(mockOctokit.repos.getLatestRelease).toHaveBeenCalledWith({
        owner,
        repo
      })
      expect(result).toEqual(mockRelease)
      expect(mockLogger.info).toHaveBeenCalledWith(`Finding latest release for ${owner}/${repo}...`)
      expect(mockLogger.info).toHaveBeenCalledWith(`Found latest release: ${mockRelease.tag_name}`)
    })
  })

  describe('downloadReleaseAssetToContainer', () => {
    test('should skip download if file already exists', async () => {
      // Mock data
      const owner = 'test-owner'
      const repo = 'test-repo'
      const releaseTag = 'v1.0.0'
      const assetName = 'test-asset.zip'
      const containerPath = '/app'
      const filePath = `${containerPath}/${assetName}`
      const chmod = '+x'

      // Setup mocks
      mockContainer.fileExistsInContainer.mockResolvedValueOnce(true)

      // Call the method
      await githubService.downloadReleaseAssetToContainer(
        owner,
        repo,
        releaseTag,
        assetName,
        mockContainer,
        containerPath,
        chmod
      )

      // Verify
      expect(mockContainer.fileExistsInContainer).toHaveBeenCalledWith(filePath)
      expect(mockLogger.info).toHaveBeenCalledWith(`File ${filePath} already exists in container, skipping download`)
      expect(mockContainer.executeContainerCommand).toHaveBeenCalledWith(
        ['chmod', chmod, filePath],
        `Failed to apply chmod ${chmod} to ${filePath}`
      )
      expect(mockOctokit.repos.getReleaseByTag).not.toHaveBeenCalled()
    })

    test('should download file if it does not exist', async () => {
      // Mock data
      const owner = 'test-owner'
      const repo = 'test-repo'
      const releaseTag = 'v1.0.0'
      const assetName = 'test-asset.zip'
      const containerPath = '/app'
      const filePath = `${containerPath}/${assetName}`

      // Mock asset data
      const mockAsset = {
        name: assetName,
        browser_download_url: 'https://example.com/download'
      }

      // Mock release data
      const mockRelease = {
        tag_name: releaseTag,
        assets: [mockAsset]
      }

      // Setup mocks
      mockContainer.fileExistsInContainer.mockResolvedValueOnce(false)
      mockOctokit.repos.getReleaseByTag.mockResolvedValueOnce({
        data: mockRelease
      })

      // Mock fetch
      global.fetch = jest.fn().mockResolvedValueOnce({
        ok: true,
        arrayBuffer: jest.fn().mockResolvedValueOnce(new ArrayBuffer(8))
      })

      // Mock container methods
      mockContainer.executeContainerCommand.mockResolvedValue('')
      mockContainer.fileExistsInContainer.mockResolvedValueOnce(true)

      // Mock exec for file creation
      const mockExec = {
        start: jest.fn().mockResolvedValueOnce({
          write: jest.fn(),
          end: jest.fn(),
          on: jest.fn().mockImplementation((event, callback) => {
            if (event === 'end') {
              callback()
            }
            return { on: jest.fn() }
          })
        }),
        inspect: jest.fn().mockResolvedValueOnce({ ExitCode: 0 })
      }
      mockContainer.exec.mockResolvedValueOnce(mockExec)

      // Call the method
      await githubService.downloadReleaseAssetToContainer(
        owner,
        repo,
        releaseTag,
        assetName,
        mockContainer,
        containerPath
      )

      // Verify
      expect(mockContainer.fileExistsInContainer).toHaveBeenCalledWith(filePath)
      expect(mockOctokit.repos.getReleaseByTag).toHaveBeenCalledWith({
        owner,
        repo,
        tag: releaseTag
      })
      expect(global.fetch).toHaveBeenCalledWith(mockAsset.browser_download_url, expect.any(Object))
      expect(mockContainer.executeContainerCommand).toHaveBeenCalledWith(
        ['mkdir', '-p', containerPath],
        'Failed to create target directory in container'
      )
      expect(mockContainer.exec).toHaveBeenCalled()
      expect(mockContainer.executeContainerCommand).toHaveBeenCalledWith(
        ['ls', '-la', containerPath],
        'Failed to list directory contents'
      )
      expect(mockContainer.fileExistsInContainer).toHaveBeenCalledWith(filePath)
    })
  })

  describe('downloadLatestReleaseFileToContainer', () => {
    test('should find latest release and download the asset', async () => {
      // Mock data
      const owner = 'test-owner'
      const repo = 'test-repo'
      const assetName = 'test-asset.zip'
      const containerPath = '/app'
      const releaseTag = 'v1.0.0'

      // Setup mocks
      const findLatestRepositoryReleaseSpy = jest.spyOn(githubService, 'findLatestRepositoryRelease')
        .mockResolvedValueOnce({ tag_name: releaseTag })

      const downloadReleaseAssetToContainerSpy = jest.spyOn(githubService, 'downloadReleaseAssetToContainer')
        .mockResolvedValueOnce()

      // Call the method
      const result = await githubService.downloadLatestReleaseFileToContainer(
        owner,
        repo,
        assetName,
        mockContainer,
        containerPath
      )

      // Verify
      expect(findLatestRepositoryReleaseSpy).toHaveBeenCalledWith(owner, repo)
      expect(downloadReleaseAssetToContainerSpy).toHaveBeenCalledWith(
        owner,
        repo,
        releaseTag,
        assetName,
        mockContainer,
        containerPath,
        null
      )
      expect(result).toBe(releaseTag)
    })
  })
})
