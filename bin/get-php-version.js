#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to get the current minimum supported PHP version
 *
 * This script outputs the current minimum supported PHP version from the config file.
 * It's used by the GitHub workflow to create a branch name for the PR.
 *
 * Usage:
 *   npm run get-php-version
 */

import { LATEST_SUPPORTED_PHP_VERSION } from '../src/Configs/php-versions.js'
import * as core from '@actions/core'
import LoggerHelper from '../src/Helpers/LoggerHelper.js'

// Output the version without any additional text
core.info(LATEST_SUPPORTED_PHP_VERSION)
