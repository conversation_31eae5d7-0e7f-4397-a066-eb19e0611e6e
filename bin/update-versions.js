#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to update the minimum supported PHP version constraint
 *
 * This script automatically fetches the current PHP version support status from php.net API
 * and determines the minimum supported PHP version. It then generates a new configuration
 * file using an EJS template and updates the test cases.
 *
 * Usage:
 *   npm run update-versions
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'
import ejs from 'ejs'
import PhpSupportStatusHelper from '../src/Helpers/PhpSupportStatusHelper.js'
import BaseError from '../src/Errors/BaseError.js'
import PhpVersionFetchError from '../src/Errors/PhpVersionFetchError.js'
import NoSupportedPhpVersionsError from '../src/Errors/NoSupportedPhpVersionsError.js'
import NoComposerRulesError from '../src/Errors/NoComposerRulesError.js'
import VersionHelper from '../src/Helpers/VersionHelper.js'
import ImportHelper from '../src/Helpers/ImportHelper.js'
import ComposerCompatibilityHelper from "../src/Helpers/ComposerCompatibilityHelper.js";
import LoggerHelper from '../src/Helpers/LoggerHelper.js';
import YamlHelper from '../src/Helpers/YamlHelper.js';

class PhpVersionUpdater {
  constructor() {
    const __dirname = path.dirname(fileURLToPath(import.meta.url))
    this.rootDir = path.resolve(__dirname, '..')

    this.phpVersionsConfigPath = path.join(this.rootDir, 'src', 'Configs', 'php-versions.yaml')
    this.composerVersionsConfigPath = path.join(this.rootDir, 'src', 'Configs', 'composer-versions.yaml')

    this.fixturesPath = path.join(this.rootDir, 'tests', 'fixtures', 'composer-projects')
  }


  async run() {
    try {
      // Fetch PHP version information
      const phpVersions = await PhpSupportStatusHelper.fetchActivePhpVersions()

      // Process Composer compatibility information using the ComposerCompatibilityResolver
      LoggerHelper.info('Fetching Composer version compatibility information...')
      const composerCompatibility = await this.processComposerCompatibility()

      // Check if we have any Composer versions
      if (!composerCompatibility || composerCompatibility.length === 0) {
        throw new NoComposerRulesError('No Composer versions found')
      }

      const versionConstraint = PhpSupportStatusHelper.determineMinimumSupportedVersion(phpVersions)
      LoggerHelper.info(`Determined minimum supported PHP version: ${versionConstraint}`)

      // Display PHP version support status
      PhpSupportStatusHelper.displaySupportStatus(phpVersions)
      await this.generateConfigFile(phpVersions, versionConstraint, composerCompatibility)
      this.updateTestCases(versionConstraint)

      LoggerHelper.info(`\n🎉 Successfully updated version configurations:`)
      LoggerHelper.info(`- PHP version constraint: ${versionConstraint}`)
      LoggerHelper.info(`- PHP versions config: ${this.phpVersionsConfigPath}`)
      LoggerHelper.info(`- Composer versions config: ${this.composerVersionsConfigPath}`)
      LoggerHelper.info(``)
      LoggerHelper.info('Remember to rebuild the project with: npm run pack')
    } catch (error) {
      if (error instanceof BaseError) {
        for (const line of error.getOutputLines()) {
          LoggerHelper.error(line)
        }
      } else {
        LoggerHelper.error(`Error: ${error.message}`)
      }
      process.exit(1)
    }
  }

  async processComposerCompatibility() {
      // Generate a compatibility matrix using the ComposerCompatibilityResolver
      const matrix = await ComposerCompatibilityHelper.generateCompatibilityMatrix ({
        includeDevVersions: false,
        includePreReleases: true
      })

      if (!matrix.packageVersions || matrix.packageVersions.length === 0) {
        throw new NoComposerRulesError('No Composer versions found in compatibility matrix')
      }

      const versionGroups = await this.readExistingComposerVersions()
      for (const version of matrix.packageVersions) {
        const composerNormalizedVersion = VersionHelper.getMajorMinorVersion(version)
        for (const phpVersion in matrix.compatibility[version]) {
          if (matrix.compatibility[version][phpVersion]) {
            if (!versionGroups[phpVersion]) {
              versionGroups[phpVersion] = composerNormalizedVersion
              continue
            }

            if (VersionHelper.compareVersions(composerNormalizedVersion, versionGroups[phpVersion]) > 0) {
              versionGroups[phpVersion] = composerNormalizedVersion
            }
          }
        }
      }

      return Object.fromEntries(
        Object.entries(versionGroups).sort((a, b) => {
          return VersionHelper.compareVersions(b[0], a[0])
        })
      )
  }

  async readExistingPhpVersions() {
    // Use the YamlHelper to read the PHP versions config
    return YamlHelper.readYamlFile(this.phpVersionsConfigPath, {})?.ALL_PHP_VERSIONS || {}
  }

  async readExistingComposerVersions() {
    // Use the YamlHelper to read the Composer versions config
    return YamlHelper.readYamlFile(this.composerVersionsConfigPath, {})?.MAX_COMPOSER_VERSION_FOR_PHP || {}
  }

  async generateConfigFile(phpVersions, versionConstraint, composerCompatibility) {
    const currentDate = new Date()
    const month = currentDate.toLocaleString('en-US', { month: 'short' })
    const year = currentDate.getFullYear()

    // Get existing versions from the config file
    const existingVersionsMap = await this.readExistingPhpVersions()

    // Extract all available PHP versions with their status
    const allPhpVersionsMap = PhpSupportStatusHelper.extractMajorMinorVersionsWithStatus(phpVersions, existingVersionsMap)

    // Get sorted list of versions for display
    const sortedVersions = PhpSupportStatusHelper.getSortedVersions(allPhpVersionsMap)

    // Get existing versions from the allPhpVersionsMap
    const existingKeys = Object.keys(allPhpVersionsMap)
    const newVersions = sortedVersions.filter(v => !existingKeys.includes(v))

    LoggerHelper.info('\nAll available PHP versions (major.minor):')
    LoggerHelper.info(sortedVersions.join(', '))

    if (newVersions.length > 0) {
      LoggerHelper.info('\nNewly added PHP versions:')
      LoggerHelper.info(newVersions.join(', '))
    }

    if (existingKeys.length > 0) {
      LoggerHelper.info('\nPreserved existing PHP versions:')
      LoggerHelper.info(existingKeys.join(', '))
    }

    LoggerHelper.info('\nPHP Version Status:')
    for (const version of sortedVersions) {
      LoggerHelper.info(`- ${version}: ${allPhpVersionsMap[version]}`)
    }

    // Generate and write YAML config file with comments preserved
    const phpYamlData = {
      LATEST_SUPPORTED_PHP_VERSION: versionConstraint,
      ALL_PHP_VERSIONS: allPhpVersionsMap
    }
    YamlHelper.writeYamlFile(this.phpVersionsConfigPath, phpYamlData)
    LoggerHelper.info(`\n✅ Generated PHP versions config file: ${this.phpVersionsConfigPath}`)

    // Generate Composer versions config file
    this.generateComposerVersionsConfig(phpVersions, sortedVersions, composerCompatibility, month, year)
  }

  #getTestCaseFilesForUpdates() {
    // Get all directories in the fixtures path and find output.json files with autoupdate-versions flag
    return fs.readdirSync(this.fixturesPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => path.join(this.fixturesPath, dirent.name, 'output.json'))
      .filter(file => ImportHelper.readJsonFile(file, {'autoupdate-versions': false})['autoupdate-versions'] === true)
  }

  generateComposerVersionsConfig(phpVersions, sortedPhpVersionsFromConfig, composerCompatibility, month, year) {
    LoggerHelper.info('\nDetermining maximum compatible Composer versions for PHP versions...')

    // Use the fetched Composer version compatibility rules
    const composerRules = composerCompatibility

    // Check if we have any composer rules
    if (!composerRules || composerRules.length === 0) {
      throw new NoComposerRulesError('Failed to fetch Composer version compatibility rules')
    }

    // Extract major.minor versions from PHP version objects and config
    const sortedPhpVersions = VersionHelper.extractPhpMajorMinorVersions(phpVersions, sortedPhpVersionsFromConfig)

    // Log the determined compatibility
    LoggerHelper.info('\nPHP to Composer version compatibility:')
    for (const phpVersion of sortedPhpVersions) {
      LoggerHelper.info(`- PHP ${phpVersion}: Composer ${composerCompatibility[phpVersion]}`)
    }

    // Generate and write YAML config file with comments preserved
    const composerYamlData = {
      MAX_COMPOSER_VERSION_FOR_PHP: composerCompatibility
    }
    YamlHelper.writeYamlFile(this.composerVersionsConfigPath, composerYamlData)
    LoggerHelper.info(`✅ Generated Composer versions config file: ${this.composerVersionsConfigPath}`)
  }



  updateTestCases(versionConstraint) {
    const testCaseFiles = this.#getTestCaseFilesForUpdates()

    let updatedCount = 0

    for (const outputFilePath of testCaseFiles) {
      // Read the JSON file using ImportHelper with a default value of false
      const outputContent = ImportHelper.readJsonFile(outputFilePath, false)

      // Skip if the file doesn't exist or can't be parsed
      if (outputContent === false) {
        LoggerHelper.warn(`Could not read or parse ${outputFilePath}`)
        continue
      }

      try {
        // Update the PHP version constraint
        outputContent['php-version'] = versionConstraint

        // Write the updated content back to the file
        fs.writeFileSync(outputFilePath, JSON.stringify(outputContent, null, 2) + '\n')
        LoggerHelper.info(`✅ Updated test case: ${outputFilePath}`)
        updatedCount++
      } catch (error) {
        LoggerHelper.warn(`Could not update ${outputFilePath}: ${error.message}`)
      }
    }

    if (updatedCount === 0) {
      LoggerHelper.info('\nNo test cases with autoupdate-versions flag found.')
    } else {
      LoggerHelper.info(`\nUpdated ${updatedCount} test case(s) with the new PHP version constraint.`)
    }
  }
}

// Create an instance and run the updater
const updater = new PhpVersionUpdater()
await updater.run()
